import random
import numpy as np
import pandas as pd
import csv
import matplotlib.pyplot as plt
import japanize_matplotlib
import copy
import os
import time

# グローバル変数
品番リスト = []
出荷数リスト = []
収容数リスト = []
サイクルタイムリスト = []
込め数リスト = []
初期在庫量リスト = []
品番数 = 0
期間 = 0

# コストとペナルティの係数
在庫コスト単価 = 180
残業コスト単価 = 66.7
段替えコスト単価 = 400
出荷遅れコスト単価 = 500

定時 = 8 * 60 * 2
最大残業時間 = 2 * 60 * 2
段替え時間 = 30

def read_csv(file_path):
    """CSVファイルを読み込む関数"""
    global 品番リスト, 出荷数リスト, 容数リスト, サイクルタイムリスト, 込め数リスト, 初期在庫量リスト
    
    収容数辞書 = {}
    try:
        with open('収容数.csv', 'r', encoding='shift-jis') as capacity_file:
            capacity_reader = csv.reader(capacity_file)
            next(capacity_reader) # ヘッダーをスキップ
            for row in capacity_reader:
                if len(row) >= 2 and row[1].strip():
                    品番 = row[0]
                    収容数 = int(float(row[1]))
                    収容数辞書[品番] = 収容数
    except FileNotFoundError:
        print("収容数.csvが見つかりません。デフォルト値（80）を使用します。")
    
    with open(file_path, 'r', encoding='shift-jis') as file:
        reader = csv.reader(file)
        header = next(reader)
        
        品番リスト.clear()
        出荷数リスト.clear()
        収容数リスト.clear()
        サイクルタイムリスト.clear()
        込め数リスト.clear()
        
        期間数 = 20 # 期間数（日数）を定義
        
        rows = list(reader)
        for row in rows:
            if len(row) == 0:
                continue
            
            total_quantity = int(row[header.index("個数")])
            
            if total_quantity < 200:
                continue
            
            daily_quantity = total_quantity / 期間数
            
            品番 = row[header.index("素材品番")]
            品番リスト.append(品番)
            出荷数リスト.append(daily_quantity)
            込め数リスト.append(int(float(row[header.index("込数")])))
            
            cycle_time_per_unit = float(row[header.index("サイクルタイム")]) / 60
            サイクルタイムリスト.append(cycle_time_per_unit)
            
            収容数リスト.append(収容数辞書.get(品番, 80))
            
        初期在庫量リスト.clear()
        for shipment in 出荷数リスト:
            random_inventory = random.randint(int(shipment * 3), int(shipment * 5))
            初期在庫量リスト.append(random_inventory)
            
    return 品番リスト, 出荷数リスト, 収容数リスト, サイクルタイムリスト, 込め数リスト, 初期在庫量リスト

def evaluate(solution, current_initial_inventory):
    """総コスト（在庫、残業、段替え、出荷遅れ）を最小化する評価関数"""
    
    total_inventory_cost = 0
    total_overtime_cost = 0
    total_setup_cost = 0
    total_shipment_delay_cost = 0
    
    inventory = current_initial_inventory[:]
    inventory_history = [[] for _ in range(品番数)]
    
    for t in range(期間):
        daily_time = 0
        daily_setup_count = 0
        
        for i in range(品番数):
            production = solution[t][i]
            
            if production > 0:
                daily_setup_count += 1
            
            setup_time = 段替え時間 if production > 0 else 0
            
            production_time = (production / 込め数リスト[i]) * サイクルタイムリスト[i]
            daily_time += production_time + setup_time
            
            inventory[i] += production - 出荷数リスト[i]
            inventory_history[i].append(inventory[i])
            
            if inventory[i] < 0:
                shortage_amount = abs(inventory[i])
                total_shipment_delay_cost += 出荷遅れコスト単価 * shortage_amount
            
            if inventory[i] > 0:
                total_inventory_cost += (inventory[i] / 収容数リスト[i]) * 在庫コスト単価
        
        total_setup_cost += 段替えコスト単価 * daily_setup_count
        
        if daily_time > 定時:
            overtime = daily_time - 定時
            total_overtime_cost += 残業コスト単価 * overtime
        
        if daily_time > 定時 + 最大残業時間:
            work_time_penalty = (daily_time - (定時 + 最大残業時間)) * (残業コスト単価 * 100000)
            total_overtime_cost += work_time_penalty
            
    total_cost = total_inventory_cost + total_overtime_cost + total_setup_cost + total_shipment_delay_cost
    
    return total_cost, inventory_history

def generate_initial_solution(current_initial_inventory):
    """
    需要予測と生産量平準化を考慮した初期解を生成する関数
    """
    solution = []
    temp_inventory = current_initial_inventory[:]
    max_daily_work_time = (8 + 2) * 60 * 2
    look_ahead_period = 3  # 3日先の需要まで見る

    for t in range(期間):
        daily_productions = [0] * 品番数
        daily_time = 0
        
        # 生産優先順位を決定
        # 在庫が少ない、かつ将来の需要が大きい品番を優先
        priority_queue = []
        for i in range(品番数):
            # 将来の総需要を計算
            future_demand = sum(出荷数リスト[i] for d in range(look_ahead_period) if t + d < 期間)
            # 在庫と将来需要のバランスを評価
            score = (temp_inventory[i] - future_demand) / 収容数リスト[i]
            priority_queue.append((score, i))
            
        priority_queue.sort()

        # 優先度の高い品番から生産量を決定
        for score, i in priority_queue:
            setup_time = 30 if daily_productions[i] == 0 else 0
            remaining_time = max_daily_work_time - daily_time
            
            if remaining_time <= setup_time:
                break
            
            cycle_time_per_unit = サイクルタイムリスト[i] / 込め数リスト[i]
            if cycle_time_per_unit == 0: continue
            
            max_producible_by_time = int((remaining_time - setup_time) / cycle_time_per_unit)
            
            # 在庫が十分か確認
            if temp_inventory[i] >= 出荷数リスト[i] * 2: # 2日分の在庫があれば今回は生産しない
                continue
            
            # 目標在庫水準（例: 3日分）に達するように生産量を計算
            target_inventory = 出荷数リスト[i] * 1
            production = max(0, target_inventory - temp_inventory[i])
            production = min(production, max_producible_by_time)
            
            if production > 0:
                daily_productions[i] = production
                daily_time += setup_time + (production / 込め数リスト[i]) * サイクルタイムリスト[i]
        
        solution.append(daily_productions)
        
        for i in range(品番数):
            temp_inventory[i] += daily_productions[i] - 出荷数リスト[i]
            
    return solution

def get_neighbors(current_solution):
    """より多様な近傍解を生成する関数"""
    neighbors = []
    num_neighbors = 30
    
    for _ in range(num_neighbors):
        neighbor = copy.deepcopy(current_solution)
        operation_type = random.choice(['swap', 'shift_time', 'adjust_amount', 'consolidate'])
        
        if operation_type == 'swap':
            t1, t2 = random.randint(0, 期間 - 1), random.randint(0, 期間 - 1)
            i1, i2 = random.randint(0, 品番数 - 1), random.randint(0, 品番数 - 1)
            if (t1, i1) != (t2, i2):
                neighbor[t1][i1], neighbor[t2][i2] = neighbor[t2][i2], neighbor[t1][i1]

        elif operation_type == 'shift_time':
            t_from = random.randint(0, 期間 - 1)
            t_to = random.randint(0, 期間 - 1)
            i = random.randint(0, 品番数 - 1)
            if t_from != t_to:
                production_amount = neighbor[t_from][i]
                if production_amount > 0:
                    neighbor[t_from][i] = 0
                    neighbor[t_to][i] += production_amount

        elif operation_type == 'adjust_amount':
            t = random.randint(0, 期間 - 1)
            i = random.randint(0, 品番数 - 1)
            change = random.randint(-100, 100)
            neighbor[t][i] = max(0, neighbor[t][i] + change)

        elif operation_type == 'consolidate':
            i = random.randint(0, 品番数 - 1)
            t_target = random.randint(0, 期間 - 1)
            total_production_for_part = sum(neighbor[t][i] for t in range(期間))
            for t in range(期間):
                neighbor[t][i] = 0
            neighbor[t_target][i] = total_production_for_part
            
        neighbors.append(neighbor)
    return neighbors

def simulated_annealing(initial_solution, current_initial_inventory, history_list):
    """
    焼きなまし法を実行する関数
    """
    current_solution = initial_solution
    current_cost, _ = evaluate(current_solution, current_initial_inventory)
    best_solution = current_solution
    best_cost = current_cost
    
    # 焼きなまし法のパラメータ
    T = 100000.0  # 初期温度
    alpha = 0.995 # 冷却率
    
    iteration_limit = 100000 # 繰り返し回数上限

    for i in range(iteration_limit):
        if T < 1e-6: # 温度が十分に下がったら終了
            break

        # 近傍解を1つ生成（より効率的な近傍解生成に切り替え）
        neighbor = get_neighbors(current_solution)[0]
        neighbor_cost, _ = evaluate(neighbor, current_initial_inventory)
        
        delta_cost = neighbor_cost - current_cost

        # 改善した場合は常に受け入れる
        if delta_cost < 0:
            current_solution = neighbor
            current_cost = neighbor_cost
            if current_cost < best_cost:
                best_solution = current_solution
                best_cost = current_cost
        else:
            # 悪化した場合は、確率で受け入れる
            acceptance_probability = np.exp(-delta_cost / T)
            if random.random() < acceptance_probability:
                current_solution = neighbor
                current_cost = neighbor_cost

        # 温度を下げる
        T *= alpha

        # 履歴を記録
        history_list.append(current_cost)
            
    return best_solution, best_cost

def multi_start_sa(num_starts, current_initial_inventory):
    """多スタート焼きなまし法を実行する関数"""
    best_solution_overall = None
    best_cost_overall = float('inf')
    search_histories = []

    for i in range(num_starts):
        print(f"--- Start {i+1}/{num_starts} ---")
        initial_solution = generate_initial_solution(current_initial_inventory)
        current_history = []
        local_optimal_solution, local_optimal_cost = simulated_annealing(initial_solution, current_initial_inventory, current_history)
        search_histories.append({'start_num': i + 1, 'history': current_history, 'final_cost': local_optimal_cost})
        
        if local_optimal_cost < best_cost_overall:
            best_cost_overall = local_optimal_cost
            best_solution_overall = local_optimal_solution
            print(f"  New best solution found with total cost: {best_cost_overall:.2f}")
            
    return best_solution_overall, best_cost_overall, search_histories

def simulate_production_schedule(initial_inventory, 期間=20):
    """生産スケジュールをシミュレートする関数（MIP用の簡易版）"""
    品番数 = len(initial_inventory)
    inventory = initial_inventory[:]
    inventory_history = [[] for _ in range(品番数)]
    
    daily_regular_time = 8 * 60 * 2
    max_daily_overtime = 2 * 60 * 2
    max_daily_work_time = daily_regular_time + max_daily_overtime
    
    for t in range(期間):
        daily_production_time = 0
        daily_setup_count = 0
        
        for i in range(品番数):
            demand = 出荷数リスト[i]
            inventory[i] -= demand
            
            if inventory[i] < 0:
                shortage = abs(inventory[i])
                production = shortage
                
                if production > 0:
                    daily_setup_count += 1
                    setup_time = 30
                    production_time = (production / 込め数リスト[i]) * サイクルタイムリスト[i]
                    daily_production_time += production_time + setup_time
                
                inventory[i] += production
            
            inventory_history[i].append(max(0, inventory[i]))
        
        if daily_production_time > max_daily_work_time:
            reduction_factor = max_daily_work_time / daily_production_time
            for i in range(品番数):
                if inventory[i] > 0:
                    current_production = max(0, 出荷数リスト[i] - (initial_inventory[i] - inventory[i]))
                    reduced_production = current_production * reduction_factor
                    inventory[i] = initial_inventory[i] + reduced_production - 出荷数リスト[i] * (t + 1)
                    inventory[i] = max(0, inventory[i])
                    inventory_history[i][-1] = inventory[i]
    return inventory_history

def adjust_initial_inventory(初期在庫量リスト, 在庫コスト単価, 出荷遅れコスト単価, num_simulations=50, max_iterations=3):
    """初期在庫量を最適化する関数（簡易シミュレーション使用版）"""
    
    品番数 = len(初期在庫量リスト)
    s = 初期在庫量リスト[:]
    
    # h / (h+c) - optimize_initial_inventoryと同じ計算方法
    prob_target = 在庫コスト単価 / (在庫コスト単価 + 出荷遅れコスト単価)
    
    print(f"\n=== 初期在庫水準の調整アルゴリズム開始 ===")
    
    for iteration in range(max_iterations):
        print(f"--- 調整イテレーション {iteration+1} ---")
        inventory_distributions = [[] for _ in range(品番数)]
        for _ in range(num_simulations):
            inventory_history = simulate_production_schedule(s)
            for i in range(品番数):
                inventory_distributions[i].extend(inventory_history[i])
        
        adjustments = [0] * 品番数
        
        for i in range(品番数):
            if not inventory_distributions[i]:
                continue
            
            inventory_counts = pd.Series(inventory_distributions[i]).value_counts(normalize=True).sort_index()
            cumulative_distribution = inventory_counts.cumsum()
            
            # sum_{x <= r-1} f(x) <= h / (h + c) - optimize_initial_inventoryと同じロジック
            best_r = 0
            for r in cumulative_distribution.index:
                prob_at_r_minus_1 = cumulative_distribution.get(r - 1, 0)
                if prob_at_r_minus_1 <= prob_target:
                    best_r = r
                else:
                    break
            
            adjustments[i] = s[i] - best_r
            
        print(f"  今回の調整量: {adjustments}")
        
        # すべての在庫点での初期在庫量をs:=s-r^*によって更新する
        new_s = [s[i] - adjustments[i] for i in range(品番数)]
        
        # 終了条件のチェック - optimize_initial_inventoryと同じ条件
        if all(abs(adj) < 1 for adj in adjustments):
            print("--- アルゴリズムが収束しました。---")
            return s
            
        s = new_s
        print(f"  更新後の初期在庫量: {s}")
        
    print("--- 最大反復回数に到達しました。---")
    return s

def plot_results(best_individual, initial_inventory, save_path=None):
    """結果をプロットする関数"""
    global 品番数, 期間, 出荷数リスト, サイクルタイムリスト, 込め数リスト

    print("\n=== 結果のプロット ===")
    total_inventory_per_period = []
    total_production_time_per_period = []
    total_setup_times_per_period = []
    total_shipment_delay_per_period = []
    inventory = initial_inventory[:]
    max_daily_work_time = (8 + 2) * 60 * 2
    daily_regular_time = 8 * 60 * 2
    
    for t in range(期間):
        daily_inventory = 0
        daily_production_time = 0
        daily_setup_times = 0
        daily_shipment_delay = 0

        for i in range(品番数):
            production = best_individual[t][i]

            if production > 0:
                daily_setup_times += 1
                setup_time = 30
            else:
                setup_time = 0

            production_time = (production / 込め数リスト[i]) * サイクルタイムリスト[i]
            daily_production_time += production_time + setup_time
            inventory[i] += production - 出荷数リスト[i]

            if inventory[i] < 0:
                daily_shipment_delay += abs(inventory[i])
                inventory[i] = 0
            daily_inventory += inventory[i]

        total_inventory_per_period.append(daily_inventory)
        total_production_time_per_period.append(daily_production_time)
        total_setup_times_per_period.append(daily_setup_times)
        total_shipment_delay_per_period.append(daily_shipment_delay)

    fig, axes = plt.subplots(2, 2, figsize=(16, 12))
    periods = list(range(1, 期間 + 1))

    axes[0, 0].bar(periods, total_inventory_per_period, color='skyblue', alpha=0.7)
    axes[0, 0].set_title('各期間の総在庫量')
    axes[0, 0].set_xlabel('期間')
    axes[0, 0].set_ylabel('総在庫量 (個)')
    axes[0, 0].grid(True, alpha=0.3)

    axes[0, 1].bar(periods, total_production_time_per_period, color='lightgreen', alpha=0.7)
    axes[0, 1].axhline(y=max_daily_work_time, color='red', linestyle='--', alpha=0.8, label=f'上限 ({max_daily_work_time}分)')
    axes[0, 1].axhline(y=daily_regular_time, color='green', linestyle='--', alpha=0.8, label=f'定時 ({daily_regular_time}分)')
    axes[0, 1].set_title('各期間の総生産時間')
    axes[0, 1].set_xlabel('期間')
    axes[0, 1].set_ylabel('総稼働時間 (分)')
    axes[0, 1].grid(True, alpha=0.3)
    axes[0, 1].legend()
    
    axes[1, 0].bar(periods, total_setup_times_per_period, color='salmon', alpha=0.7)
    axes[1, 0].set_title('各期間の総段替え回数')
    axes[1, 0].set_xlabel('期間')
    axes[1, 0].set_ylabel('総段替え回数（回）')
    axes[1, 0].grid(True, alpha=0.3)

    axes[1, 1].bar(periods, total_shipment_delay_per_period, color='gold', alpha=0.7)
    axes[1, 1].set_title('各期間の総出荷遅れ量')
    axes[1, 1].set_xlabel('期間')
    axes[1, 1].set_ylabel('総出荷遅れ量 (個)')
    axes[1, 1].grid(True, alpha=0.3)
    
    # 時間制約違反のチェック
    time_violations = sum(1 for x in total_production_time_per_period if x > max_daily_work_time)
    print(f"時間制約違反: {time_violations} 期間")

    plt.tight_layout()
    if save_path:
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        print(f"プロットを保存: {save_path}")
    else:
        plt.show()

def plot_search_history(search_histories, save_path=None):
    """各スタートの探索過程（コストの推移）をプロットする関数"""
    plt.figure(figsize=(12, 8))
    for history_data in search_histories:
        history = history_data['history']
        start_num = history_data['start_num']
        x_axis = range(len(history))
        plt.plot(x_axis, history, label=f'スタート {start_num}')
    plt.title('各スタートの焼きなまし法探索履歴')
    plt.xlabel('ステップ数')
    plt.ylabel('総コスト')
    plt.grid(True, linestyle='--', alpha=0.6)
    plt.legend(bbox_to_anchor=(1.05, 1), loc='upper left')
    plt.tight_layout(rect=[0, 0, 0.85, 1])
    if save_path:
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        print(f"探索履歴プロットを保存: {save_path}")
    else:
        plt.show()
    plt.close()

def process_single_file(file_path):
    """単一のCSVファイルを処理する関数"""
    global 品番数, 期間
    print(f"\n=== Processing {file_path} ===")
    
    品番リスト, 出荷数リスト, 収容数リスト, サイクルタイムリスト, 込め数リスト, 初期在庫量リスト = read_csv(file_path)
    品番数 = len(品番リスト)
    期間 = 20
    
    adjusted_initial_inventory = adjust_initial_inventory(初期在庫量リスト, 在庫コスト単価, 出荷遅れコスト単価, num_simulations=50, max_iterations=3)
    初期在庫量リスト[:] = adjusted_initial_inventory
    
    print("=== 多スタート焼きなまし法 スケジューリング ===")
    start_time = time.time()
    num_starts = 5 # 試行回数を増やして探索を強化
    best_solution, best_cost, search_histories = multi_start_sa(num_starts, 初期在庫量リスト)
    calculation_time = time.time() - start_time
    
    if best_solution:
        print(f"\n=== 最適化結果 ===")
        print(f"最良個体の総コスト: {best_cost:.2f}")
        print(f"計算時間: {calculation_time:.2f}秒")
        
        base_name = os.path.splitext(os.path.basename(file_path))[0]
        result_csv_path = f"result/sa_results_{base_name}.csv"
        plot_path = f"result/sa_results_{base_name}.png"
        
        plot_search_history(search_histories)
        plot_results(best_solution, 初期在庫量リスト, plot_path)
        
        return best_cost, calculation_time, base_name
    else:
        print("\n解が見つかりませんでした")
        return None, None, None

def main():
    """メイン実行関数 - 全CSVファイルを処理"""
    data_folder = "data"
    
    if not os.path.exists(data_folder):
        print(f"'{data_folder}' フォルダが見つかりません。データファイルを配置してください。")
        return

    csv_files = [f for f in os.listdir(data_folder) if f.endswith('.csv')]
    
    if not csv_files:
        print(f"'{data_folder}' フォルダにCSVファイルが見つかりません")
        return
    
    if not os.path.exists('result'):
        os.makedirs('result')
    
    print(f"見つかったCSVファイル: {csv_files}")
    
    all_results = []
    total_objective_value = 0
    total_calculation_time = 0
    
    for csv_file in csv_files:
        file_path = os.path.join(data_folder, csv_file)
        objective_value, calc_time, file_name = process_single_file(file_path)
        
        if objective_value is not None:
            all_results.append({
                'ファイル名': file_name,
                '目的関数値': objective_value,
                '計算時間': calc_time
            })
            total_objective_value += objective_value
            total_calculation_time += calc_time
    
    summary_df = pd.DataFrame(all_results)
    
    summary_row = pd.DataFrame({
        'ファイル名': ['合計'],
        '目的関数値': [total_objective_value],
        '計算時間': [total_calculation_time]
    })
    summary_df = pd.concat([summary_df, summary_row], ignore_index=True)
    
    summary_csv_path = "result/simulated_annealing_aggregate_results.csv"
    summary_df.to_csv(summary_csv_path, index=False, encoding='shift-jis')
    print(f"\n集計結果をCSVファイルに保存: {summary_csv_path}")
    
    print(f"\n=== 全体の集計結果 ===")
    print(f"処理したファイル数: {len(all_results)}")
    print(f"総目的関数値: {total_objective_value:.2f}")
    print(f"総計算時間: {total_calculation_time:.2f}秒")
    if len(all_results) > 0:
        print(f"平均目的関数値: {total_objective_value/len(all_results):.2f}")
        print(f"平均計算時間: {total_calculation_time/len(all_results):.2f}秒")
        
if __name__ == "__main__":
    main()





