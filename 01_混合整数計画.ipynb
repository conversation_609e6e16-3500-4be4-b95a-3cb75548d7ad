import pandas as pd
import pulp
import csv
import random
import matplotlib.pyplot as plt
import japanize_matplotlib
import copy

# グローバル変数
品番リスト = []
出荷数リスト = []
収容数リスト = []
サイクルタイムリスト = []
込め数リスト = []
初期在庫量リスト = []

# コストとペナルティの係数
在庫コスト単価 = 180
残業コスト単価 = 66.7
段替えコスト単価 = 400
出荷遅れコスト単価 = 500

定時 = 8 * 60 * 2
最大残業時間 = 2 * 60 * 2
段替え時間 = 30

def read_csv(file_path):
    """CSVファイルを読み込む関数"""
    global 品番リスト, 出荷数リスト, 収容数リスト, サイクルタイムリスト, 込め数リスト, 初期在庫量リスト
    
    収容数辞書 = {}
    with open('収容数.csv', 'r', encoding='shift-jis') as capacity_file:
        capacity_reader = csv.reader(capacity_file)
        capacity_header = next(capacity_reader)
        for row in capacity_reader:
            if len(row) >= 2 and row[1].strip():  # Skip if second column is empty
                品番 = row[0]  # 品番列
                収容数 = int(float(row[1]))  # 収容数列
                収容数辞書[品番] = 収容数

    with open(file_path, 'r', encoding='shift-jis') as file:
        reader = csv.reader(file)
        header = next(reader)
        
        品番リスト = []
        出荷数リスト = []
        収容数リスト = []
        サイクルタイムリスト = []
        込め数リスト = []
        
        # 期間数（日数）を定義
        期間数 = 20
        
        rows = list(reader)
        for row in rows:
            if len(row) == 0:
                continue
            
            # 個数を取得
            total_quantity = int(row[header.index("個数")])
            
            # 個数が200未満の場合はスキップ
            if total_quantity < 200:
                continue
            
            # 1日あたりの出荷数を計算（総期間で割る）
            daily_quantity = total_quantity / 期間数
            
            品番リスト.append(row[header.index("素材品番")])
            出荷数リスト.append(daily_quantity)
            込め数リスト.append(int(float(row[header.index("込数")])))
            
            cycle_time_per_unit = float(row[header.index("サイクルタイム")]) / 60
            サイクルタイムリスト.append(cycle_time_per_unit)
            
            収容数リスト.append(収容数辞書.get(品番, 80))
            
    
        # 出荷数に基づいて初期在庫量（処理前）をランダムに設定
        初期在庫量リスト = []
        for shipment in 出荷数リスト:
            random_inventory = random.randint(int(shipment * 3), int(shipment * 5))
            初期在庫量リスト.append(random_inventory)
            
    return 品番リスト, 出荷数リスト, 収容数リスト, サイクルタイムリスト, 込め数リスト, 初期在庫量リスト

def solve_mip(initial_inventory_list_arg):
    """PuLPを用いてMIPを解く関数"""
    
    # モデルの定義
    model = pulp.LpProblem("ProductionScheduling", pulp.LpMinimize)
    
    # インデックスの定義
    品目 = range(品番数)
    期間_index = range(期間)

    # 決定変数
    Production = pulp.LpVariable.dicts("Production", (品目, 期間_index), lowBound=0, cat='Integer')
    IsProduced = pulp.LpVariable.dicts("IsProduced", (品目, 期間_index), cat='Binary')
    Inventory = pulp.LpVariable.dicts("Inventory", (品目, 期間_index), lowBound=0, cat='Continuous')
    Shortage = pulp.LpVariable.dicts("Shortage", (品目, 期間_index), lowBound=0, cat='Continuous')
    WorkTime = pulp.LpVariable.dicts("WorkTime", 期間_index, lowBound=0, cat='Continuous')
    Overtime = pulp.LpVariable.dicts("Overtime", 期間_index, lowBound=0, cat='Continuous')

    # 目的関数
    total_cost = pulp.lpSum(
        在庫コスト単価 * Inventory[i][t]/収容数リスト[i] for i in 品目 for t in 期間_index
    ) + pulp.lpSum(
        残業コスト単価 * Overtime[t] for t in 期間_index
    ) + pulp.lpSum(
        段替えコスト単価 * IsProduced[i][t] for i in 品目 for t in 期間_index
    ) + pulp.lpSum(
        出荷遅れコスト単価 * Shortage[i][t] for i in 品目 for t in 期間_index
    )
    
    model += total_cost, "Total Cost"

    # 制約条件
    bigM = 1000000

    for i in 品目:
        for t in 期間_index:
            if t == 0:
                # 初期在庫リストを使用
                model += Inventory[i][t] - Shortage[i][t] == initial_inventory_list_arg[i] + Production[i][t] - 出荷数リスト[i]
            else:
                model += Inventory[i][t] - Shortage[i][t] == Inventory[i][t-1] - Shortage[i][t-1] + Production[i][t] - 出荷数リスト[i]
            
            model += Production[i][t] <= bigM * IsProduced[i][t]

    for t in 期間_index:
        model += WorkTime[t] == pulp.lpSum(
            Production[i][t] * (サイクルタイムリスト[i] / 込め数リスト[i]) + 段替え時間 * IsProduced[i][t]
            for i in 品目
        )
        
        model += WorkTime[t] <= 定時 + Overtime[t]
        model += WorkTime[t] <= 定時 + 最大残業時間
        model += Overtime[t] >= WorkTime[t] - 定時
        model += Overtime[t] >= 0

    # Solverの設定
    solver = pulp.GUROBI(msg=True, timelimit=500)
    
    # 最適化の実行
    model.solve(solver)
    
    # ソルバーの詳細情報を取得
    status = pulp.LpStatus[model.status]
    print("ステータス:", status)
    
    # 精度情報を格納する辞書
    accuracy_info = {}
    
    # Gurobiソルバーの詳細情報を取得
    if hasattr(solver, 'solverModel') and solver.solverModel is not None:
        gurobi_model = solver.solverModel
        
        # 最適性ギャップの取得
        if hasattr(gurobi_model, 'MIPGap'):
            gap = gurobi_model.MIPGap * 100  # パーセンテージに変換
            accuracy_info['gap'] = gap
            print(f"最適性ギャップ: {gap:.4f}%")
        
        # 目的関数値と境界値の取得
        if hasattr(gurobi_model, 'ObjVal'):
            obj_val = gurobi_model.ObjVal
            accuracy_info['obj_val'] = obj_val
            print(f"目的関数値: {obj_val:.2f}")
            
        if hasattr(gurobi_model, 'ObjBound'):
            obj_bound = gurobi_model.ObjBound
            accuracy_info['obj_bound'] = obj_bound
            print(f"目的関数境界値: {obj_bound:.2f}")
            
        # ソルバーステータスの詳細
        if hasattr(gurobi_model, 'Status'):
            gurobi_status = gurobi_model.Status
            accuracy_info['gurobi_status'] = gurobi_status
            print(f"Gurobiステータス: {gurobi_status}")
            
            # ステータスの意味を表示
            status_meanings = {
                1: "LOADED (モデルが読み込まれた)",
                2: "OPTIMAL (最適解が見つかった)",
                3: "INFEASIBLE (実行不可能)",
                4: "INF_OR_UNBD (実行不可能または非有界)",
                5: "UNBOUNDED (非有界)",
                6: "CUTOFF (カットオフ値により終了)",
                7: "ITERATION_LIMIT (反復回数制限により終了)",
                8: "NODE_LIMIT (ノード数制限により終了)",
                9: "TIME_LIMIT (時間制限により終了)",
                10: "SOLUTION_LIMIT (解の数制限により終了)",
                11: "INTERRUPTED (ユーザーにより中断)",
                12: "NUMERIC (数値的困難)",
                13: "SUBOPTIMAL (準最適解)",
                14: "INPROGRESS (進行中)",
                15: "USER_OBJ_LIMIT (ユーザー目的関数制限により終了)"
            }
            if gurobi_status in status_meanings:
                accuracy_info['status_meaning'] = status_meanings[gurobi_status]
                print(f"ステータスの意味: {status_meanings[gurobi_status]}")
    
    if status == 'Optimal':
        print("総コスト:", pulp.value(model.objective))

        production_schedule = [[0] * 期間 for _ in range(品番数)]
        for i in 品目:
            for t in 期間_index:
                production_schedule[i][t] = pulp.value(Production[i][t])

        return production_schedule, pulp.value(model.objective), accuracy_info
    elif status == 'Not Solved':
        # 時間制限などで最適解が見つからなかった場合でも、実行可能解があれば取得
        if pulp.value(model.objective) is not None:
            print("時間制限により最適解は見つかりませんでしたが、実行可能解を取得しました")
            print("総コスト:", pulp.value(model.objective))
            
            production_schedule = [[0] * 期間 for _ in range(品番数)]
            for i in 品目:
                for t in 期間_index:
                    production_schedule[i][t] = pulp.value(Production[i][t])

            return production_schedule, pulp.value(model.objective), accuracy_info
    
    return None, None, accuracy_info


def simulate_production_schedule(initial_inventory, 期間=20):
    """生産スケジュールをシミュレートする関数（MIP用の簡易版）"""
    品番数 = len(initial_inventory)
    inventory = initial_inventory[:]
    inventory_history = [[] for _ in range(品番数)]
    
    daily_regular_time = 8 * 60 * 2  # 1日の通常稼働時間（分）
    max_daily_overtime = 2 * 60 * 2  # 1日の最大残業時間（分）
    max_daily_work_time = daily_regular_time + max_daily_overtime
    
    for t in range(期間):
        daily_production_time = 0
        daily_setup_count = 0
        
        # 各品番の需要を処理
        for i in range(品番数):
            demand = 出荷数リスト[i]
            inventory[i] -= demand
            
            # 在庫が不足する場合は生産
            if inventory[i] < 0:
                shortage = abs(inventory[i])
                # 生産量を決定（不足分を補う）
                production = shortage
                
                # 生産時間を計算
                if production > 0:
                    daily_setup_count += 1
                    setup_time = 30  # 段替え時間
                    production_time = (production / 込め数リスト[i]) * サイクルタイムリスト[i]
                    daily_production_time += production_time + setup_time
                
                inventory[i] += production
            
            # 在庫履歴に記録
            inventory_history[i].append(max(0, inventory[i]))
        
        # 稼働時間制約チェック（制約違反の場合は一部生産を削減）
        if daily_production_time > max_daily_work_time:
            # 簡易的な調整：超過分を比例配分で削減
            reduction_factor = max_daily_work_time / daily_production_time
            for i in range(品番数):
                if inventory[i] > 0:
                    # 生産量を削減し、在庫を調整
                    current_production = max(0, 出荷数リスト[i] - (initial_inventory[i] - inventory[i]))
                    reduced_production = current_production * reduction_factor
                    inventory[i] = initial_inventory[i] + reduced_production - 出荷数リスト[i] * (t + 1)
                    inventory[i] = max(0, inventory[i])
                    inventory_history[i][-1] = inventory[i]
    
    return inventory_history

def adjust_initial_inventory(初期在庫量リスト, 在庫コスト単価, 出荷遅れコスト単価, num_simulations=50, max_iterations=3):
    """初期在庫を更新する関数（optimize_initial_inventoryと一致させた版）"""
    
    品番数 = len(初期在庫量リスト)
    s = 初期在庫量リスト[:]
    
    # h / (h+c) - optimize_initial_inventoryと同じ計算方法
    prob_target = 在庫コスト単価 / (在庫コスト単価 + 出荷遅れコスト単価)
    
    print(f"\n=== 初期在庫水準の調整アルゴリズム開始 ===")
    
    for iteration in range(max_iterations):
        print(f"--- 調整イテレーション {iteration + 1} ---")
        
        # 各在庫点について在庫量の分布を求める
        inventory_distributions = [[] for _ in range(品番数)]
        for _ in range(num_simulations):
            inventory_history = simulate_production_schedule(s)
            for i in range(品番数):
                inventory_distributions[i].extend(inventory_history[i])
        
        adjustments = [0] * 品番数
        
        # 各在庫点について在庫量の最適調整量r^*を求める
        for i in range(品番数):
            if not inventory_distributions[i]:
                continue
            
            inventory_counts = pd.Series(inventory_distributions[i]).value_counts(normalize=True).sort_index()
            cumulative_distribution = inventory_counts.cumsum()
            
            # sum_{x <= r-1} f(x) <= h / (h + c) - optimize_initial_inventoryと同じロジック
            best_r = 0
            for r in cumulative_distribution.index:
                prob_at_r_minus_1 = cumulative_distribution.get(r - 1, 0)
                if prob_at_r_minus_1 <= prob_target:
                    best_r = r
                else:
                    break
            
            adjustments[i] = s[i] - best_r
            
        print(f"  今回の調整量: {adjustments}")
        
        # すべての在庫点での初期在庫量をs:=s-r^*によって更新する
        new_s = [s[i] - adjustments[i] for i in range(品番数)]
        
        # 終了条件のチェック - optimize_initial_inventoryと同じ条件
        if all(abs(adj) < 1 for adj in adjustments):
            print("--- アルゴリズムが収束しました。---")
            return s
            
        s = new_s
        print(f"  更新後の初期在庫量: {s}")
        
    print("--- 最大反復回数に到達しました。---")
    return s

def plot_results(best_individual, initial_inventory, save_path=None):
    """結果をプロットする関数"""
    global 品番数, 期間, 品番リスト, 出荷数リスト, サイクルタイムリスト, 込め数リスト
    
    print("\n=== 結果のプロット ===")
    
    total_inventory_per_period = []
    total_production_time_per_period = []
    total_setup_times_per_period = []
    total_shipment_delay_per_period = []

    inventory = initial_inventory[:]
    max_daily_work_time = (8 + 2) * 60 * 2
    daily_regular_time = 8 * 60 * 2
    
    for t in range(期間):
        daily_inventory = 0
        daily_production_time = 0
        daily_setup_times = 0
        daily_shipment_delay = 0
        
        for i in range(品番数):
            production = best_individual[i][t]
            
            if production > 0:
                daily_setup_times += 1
                setup_time = 30
            else:
                setup_time = 0

            production_time = (production / 込め数リスト[i]) * サイクルタイムリスト[i]
            daily_production_time += production_time + setup_time
            
            inventory[i] += production - 出荷数リスト[i]

            if inventory[i] < 0:
                daily_shipment_delay += abs(inventory[i])
                inventory[i] = 0

            daily_inventory += inventory[i]
            
        total_inventory_per_period.append(daily_inventory)
        total_production_time_per_period.append(daily_production_time)
        total_setup_times_per_period.append(daily_setup_times)
        total_shipment_delay_per_period.append(daily_shipment_delay)

    fig, axes = plt.subplots(2, 2, figsize=(16, 12))
    periods = list(range(1, 期間 + 1))

    # 1. 各期間の総在庫量
    axes[0, 0].bar(periods, total_inventory_per_period, color='skyblue', alpha=0.7)
    axes[0, 0].set_title('各期間の総在庫量')
    axes[0, 0].set_xlabel('期間')
    axes[0, 0].set_ylabel('総在庫量 (個)')
    axes[0, 0].grid(True, alpha=0.3)
    if total_inventory_per_period:
        axes[0, 0].set_ylim(0, max(total_inventory_per_period) * 1.2)

    # 2. 各期間の総生産時間＋制限ライン
    axes[0, 1].bar(periods, total_production_time_per_period, color='lightgreen', alpha=0.7)
    axes[0, 1].axhline(y=max_daily_work_time, color='red', linestyle='--', alpha=0.8, label=f'上限 ({max_daily_work_time}分)')
    axes[0, 1].axhline(y=daily_regular_time, color='green', linestyle='--', alpha=0.8, label=f'定時 ({daily_regular_time}分)')
    axes[0, 1].set_title('各期間の総生産時間')
    axes[0, 1].set_xlabel('期間')
    axes[0, 1].set_ylabel('総稼働時間 (分)')
    axes[0, 1].grid(True, alpha=0.3)
    if total_production_time_per_period:
        axes[0, 1].set_ylim(0, max(max_daily_work_time, max(total_production_time_per_period)) * 1.2)
    axes[0, 1].legend()

    # 3. 各期間の総段替え回数
    axes[1, 0].bar(periods, total_setup_times_per_period, color='salmon', alpha=0.7)
    axes[1, 0].set_title('各期間の総段替え回数')
    axes[1, 0].set_xlabel('期間')
    axes[1, 0].set_ylabel('総段替え回数（回）')
    axes[1, 0].grid(True, alpha=0.3)
    if total_setup_times_per_period:
        axes[1, 0].set_ylim(0, max(total_setup_times_per_period) * 1.2)

    # 4. 各期間の総出荷遅れ量
    axes[1, 1].bar(periods, total_shipment_delay_per_period, color='gold', alpha=0.7)
    axes[1, 1].set_title('各期間の総出荷遅れ量')
    axes[1, 1].set_xlabel('期間')
    axes[1, 1].set_ylabel('総出荷遅れ量 (個)')
    axes[1, 1].grid(True, alpha=0.3)
    if total_shipment_delay_per_period:
        axes[1, 1].set_ylim(0, max(total_shipment_delay_per_period) * 1.2)

    plt.tight_layout()
    
    if save_path:
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        print(f"プロットを保存: {save_path}")
    else:
        plt.show()

    time_violations = sum(1 for x in total_production_time_per_period if x > daily_regular_time + max_daily_work_time)
    print(f"時間制約違反: {time_violations} 期間")
    
import os
import time
    
def process_single_file(file_path):
    """単一のCSVファイルを処理する関数"""
    global 品番数, 期間, 初期在庫量リスト
    
    print(f"\n=== Processing {file_path} ===")
    
    # CSVファイルを読み込み
    品番リスト, 出荷数リスト, 収容数リスト, サイクルタイムリスト, 込め数リスト, 初期在庫量リスト = read_csv(file_path)
    品番数 = len(品番リスト)
    期間 = 20
    在庫コスト単価 = 180
    出荷遅れコスト単価 = 500
    
    # 初期在庫量を調整
    adjusted_initial_inventory = adjust_initial_inventory(初期在庫量リスト, 在庫コスト単価, 出荷遅れコスト単価, num_simulations=50, max_iterations=3)
    初期在庫量リスト = adjusted_initial_inventory
    
    print("=== 混合整数計画法 スケジューリング ===")
    
    # 計算時間を測定
    start_time = time.time()
    best_solution, best_cost, accuracy_info = solve_mip(初期在庫量リスト)
    calculation_time = time.time() - start_time
    
    if best_solution:
        print(f"\n=== 最適化結果 ===")
        print(f"最良個体の総コスト: {best_cost:.2f}")
        print(f"計算時間: {calculation_time:.2f}秒")
        
        # 精度情報の表示
        if accuracy_info:
            print(f"\n=== 解の精度情報 ===")
            if 'gap' in accuracy_info:
                print(f"最適性ギャップ: {accuracy_info['gap']:.4f}%")
            if 'obj_val' in accuracy_info and 'obj_bound' in accuracy_info:
                print(f"目的関数値: {accuracy_info['obj_val']:.2f}")
                print(f"目的関数境界値: {accuracy_info['obj_bound']:.2f}")
                gap_manual = abs(accuracy_info['obj_val'] - accuracy_info['obj_bound']) / abs(accuracy_info['obj_val']) * 100
                print(f"手動計算ギャップ: {gap_manual:.4f}%")
            if 'gurobi_status' in accuracy_info:
                print(f"Gurobiステータス: {accuracy_info['gurobi_status']}")
            if 'status_meaning' in accuracy_info:
                print(f"ステータスの意味: {accuracy_info['status_meaning']}")
        
        # 結果をDataFrameに変換
        result_df = pd.DataFrame(best_solution, 
                                index=[f"品番_{i+1}" for i in range(品番数)],
                                columns=[f"期間_{t+1}" for t in range(期間)])
        
        # ファイル名から拡張子を除去
        base_name = os.path.splitext(os.path.basename(file_path))[0]
        
        # 結果をCSVとして保存
        result_csv_path = f"result//MIP_results_{base_name}.csv"
        result_df.to_csv(result_csv_path, encoding='shift-jis')
        print(f"結果をCSVファイルに保存: {result_csv_path}")
        
        # プロットを作成して保存
        plot_path = f"result//MIP_results_{base_name}.png"
        plot_results(best_solution, 初期在庫量リスト, save_path=plot_path)
        
        return best_cost, calculation_time, base_name
    else:
        print("\n解が見つかりませんでした")
        return None, None, None

def main():
    """メイン実行関数 - 全CSVファイルを処理"""
    data_folder = "data"
    
    # dataフォルダ内のすべてのCSVファイルを取得
    csv_files = [f for f in os.listdir(data_folder) if f.endswith('.csv')]
    
    if not csv_files:
        print("dataフォルダにCSVファイルが見つかりません")
        return
    
    print(f"見つかったCSVファイル: {csv_files}")
    
    # 結果を格納するリスト
    all_results = []
    total_objective_value = 0
    total_calculation_time = 0
    
    # 各CSVファイルを処理
    for csv_file in csv_files:
        file_path = os.path.join(data_folder, csv_file)
        objective_value, calc_time, file_name = process_single_file(file_path)
        
        if objective_value is not None:
            all_results.append({
                'ファイル名': file_name,
                '目的関数値': objective_value,
                '計算時間': calc_time
            })
            total_objective_value += objective_value
            total_calculation_time += calc_time
    
    # 集計結果をDataFrameに変換
    summary_df = pd.DataFrame(all_results)
    
    # 合計行を追加
    summary_row = pd.DataFrame({
        'ファイル名': ['合計'],
        '目的関数値': [total_objective_value],
        '計算時間': [total_calculation_time]
    })
    summary_df = pd.concat([summary_df, summary_row], ignore_index=True)
    
    # 集計結果をCSVとして保存
    summary_csv_path = "result//MIP_aggregate_results.csv"
    summary_df.to_csv(summary_csv_path, encoding='shift-jis', index=False)
    print(f"\n集計結果をCSVファイルに保存: {summary_csv_path}")
    
    # 集計結果のプロットを作成
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 6))
    
    # 目的関数値のプロット（合計を除く）
    individual_results = summary_df[summary_df['ファイル名'] != '合計']
    ax1.bar(individual_results['ファイル名'], individual_results['目的関数値'], 
            color='skyblue', alpha=0.7)
    ax1.set_title('各データセットの目的関数値')
    ax1.set_xlabel('データセット')
    ax1.set_ylabel('目的関数値')
    ax1.tick_params(axis='x', rotation=45)
    ax1.grid(True, alpha=0.3)
    
    # 計算時間のプロット（合計を除く）
    ax2.bar(individual_results['ファイル名'], individual_results['計算時間'], 
            color='lightgreen', alpha=0.7)
    ax2.set_title('各データセットの計算時間')
    ax2.set_xlabel('データセット')
    ax2.set_ylabel('計算時間 (秒)')
    ax2.tick_params(axis='x', rotation=45)
    ax2.grid(True, alpha=0.3)
    
    plt.tight_layout()
    
    # 集計プロットを保存
    #aggregate_plot_path = "aggregate_results.png"
    #plt.savefig(aggregate_plot_path, dpi=300, bbox_inches='tight')
    #plt.show()
    #print(f"集計プロットを画像ファイルに保存: {aggregate_plot_path}")
    
    # 結果の要約を表示
    print(f"\n=== 全体の集計結果 ===")
    print(f"処理したファイル数: {len(all_results)}")
    print(f"総目的関数値: {total_objective_value:.2f}")
    print(f"総計算時間: {total_calculation_time:.2f}秒")
    print(f"平均目的関数値: {total_objective_value/len(all_results):.2f}")
    print(f"平均計算時間: {total_calculation_time/len(all_results):.2f}秒")
    
if __name__ == "__main__":
    main()