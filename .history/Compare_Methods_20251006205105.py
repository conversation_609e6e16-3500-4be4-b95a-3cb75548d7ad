#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
MIPと多スタート焼きなまし法の比較実行スクリプト
"""

import os
import glob
import time
import pandas as pd
import matplotlib.pyplot as plt
import japanize_matplotlib
from process import (
    read_csv, adjust_initial_inventory, solve_mip, multi_start_simulated_annealing,
    plot_result, save_results_to_csv, calculate_summary_statistics,
    plot_aggregate_results
)
# グローバル変数をインポート
import process

def run_comparison_for_single_file(csv_file, mip_time_limit=500, sa_params=None):
    """単一ファイルに対してMIPと焼きなまし法の両方を実行して比較する関数"""
    if sa_params is None:
        sa_params = {
            'num_starts': 5,
            'max_iterations': 1000,
            'initial_temp': 1000,
            'cooling_rate': 0.95
        }
    
    file_name = os.path.basename(csv_file).replace('.csv', '')
    print(f"\n=== Processing {csv_file} ===")
    
    try:
        # データを読み込み
        read_csv(csv_file)
        
        # 初期在庫調整
        print("\n=== 初期在庫水準の調整アルゴリズム開始 ===")
        adjusted_inventory = adjust_initial_inventory(process.初期在庫量リスト)
        
        results = []
        
        # MIP実行
        print("\n=== 混合整数計画法 スケジューリング ===")
        mip_start_time = time.time()
        mip_schedule, mip_cost, mip_accuracy = solve_mip(adjusted_inventory, mip_time_limit)
        mip_time = time.time() - mip_start_time
        
        if mip_schedule is not None:
            print(f"MIP総コスト: {mip_cost:.2f}")
            print(f"MIP計算時間: {mip_time:.2f}秒")
            
            mip_result = {
                'データファイル': file_name,
                '手法': 'MIP',
                '総コスト': mip_cost,
                '計算時間': mip_time,
                '品番数': len(process.品番リスト),
                '調整前初期在庫合計': sum(process.初期在庫量リスト),
                '調整後初期在庫合計': sum(adjusted_inventory),
                '在庫削減量': sum(process.初期在庫量リスト) - sum(adjusted_inventory),
                '在庫削減率(%)': ((sum(process.初期在庫量リスト) - sum(adjusted_inventory)) / sum(process.初期在庫量リスト)) * 100 if sum(process.初期在庫量リスト) > 0 else 0
            }
            
            # MIP結果をプロット・保存
            mip_violations = plot_result(mip_schedule, adjusted_inventory, file_name, "MIP")
            mip_result['時間制約違反期間数'] = mip_violations
            save_results_to_csv([mip_result], f'MIP_results_{file_name}.csv')
            
            results.append(mip_result)
        else:
            print("MIPの求解に失敗しました。")
        
        # 焼きなまし法実行
        print(f"\n=== 多スタート焼きなまし法 スケジューリング ===")
        sa_start_time = time.time()
        sa_schedule, sa_cost = multi_start_simulated_annealing(adjusted_inventory, **sa_params)
        sa_time = time.time() - sa_start_time
        
        print(f"SA総コスト: {sa_cost:.2f}")
        print(f"SA計算時間: {sa_time:.2f}秒")
        
        sa_result = {
            'データファイル': file_name,
            '手法': 'SA',
            '総コスト': sa_cost,
            '計算時間': sa_time,
            '品番数': len(process.品番リスト),
            '調整前初期在庫合計': sum(process.初期在庫量リスト),
            '調整後初期在庫合計': sum(adjusted_inventory),
            '在庫削減量': sum(process.初期在庫量リスト) - sum(adjusted_inventory),
            '在庫削減率(%)': ((sum(process.初期在庫量リスト) - sum(adjusted_inventory)) / sum(process.初期在庫量リスト)) * 100 if sum(process.初期在庫量リスト) > 0 else 0
        }
        
        # SA結果をプロット・保存
        sa_violations = plot_result(sa_schedule, adjusted_inventory, file_name, "SA")
        sa_result['時間制約違反期間数'] = sa_violations
        save_results_to_csv([sa_result], f'SA_results_{file_name}.csv')
        
        results.append(sa_result)
        
        # 比較結果を表示
        if mip_schedule is not None:
            cost_diff = sa_cost - mip_cost
            time_diff = sa_time - mip_time
            
            print(f"\n=== 比較結果 ===")
            print(f"コスト差 (SA - MIP): {cost_diff:.2f}")
            print(f"時間差 (SA - MIP): {time_diff:.2f}秒")
            print(f"MIPの方が{'良い' if cost_diff > 0 else '悪い'}解: {abs(cost_diff):.2f}")
            print(f"SAの方が{'速い' if time_diff < 0 else '遅い'}: {abs(time_diff):.2f}秒")
        
        return results
        
    except Exception as e:
        print(f"エラーが発生しました ({csv_file}): {e}")
        return []

def run_comparison_batch(data_folder='data', mip_time_limit=500, sa_params=None):
    """データフォルダ内の全CSVファイルに対して比較実行する関数"""
    csv_files = glob.glob(os.path.join(data_folder, '*.csv'))
    
    if not csv_files:
        print(f"データフォルダ '{data_folder}' にCSVファイルが見つかりません。")
        return None
    
    print(f"見つかったCSVファイル: {[os.path.basename(f) for f in csv_files]}")
    
    all_results = []
    mip_results = []
    sa_results = []
    total_start_time = time.time()
    
    for csv_file in csv_files:
        file_results = run_comparison_for_single_file(csv_file, mip_time_limit, sa_params)
        
        for result in file_results:
            all_results.append(result)
            if result['手法'] == 'MIP':
                mip_results.append(result)
            elif result['手法'] == 'SA':
                sa_results.append(result)
    
    total_time = time.time() - total_start_time
    
    if all_results:
        print(f"\n=== バッチ比較処理完了 ===")
        print(f"総処理時間: {total_time:.2f}秒")
        print(f"処理ファイル数: {len(csv_files)}")
        print(f"MIP結果数: {len(mip_results)}")
        print(f"SA結果数: {len(sa_results)}")
        
        # 全結果を保存
        all_csv = save_results_to_csv(all_results, 'Comparison_all_results.csv')
        
        # 手法別比較プロット
        plot_filename, comparison_csv = compare_optimization_results(mip_results, sa_results, "手法比較")
        
        # 統計情報を計算
        summary_df, stats_csv = calculate_summary_statistics(all_results, "比較統計")
        
        # 詳細比較分析
        comparison_analysis = analyze_comparison_results(mip_results, sa_results)
        
        return {
            'all_results': all_results,
            'mip_results': mip_results,
            'sa_results': sa_results,
            'all_csv': all_csv,
            'comparison_csv': comparison_csv,
            'stats_csv': stats_csv,
            'plot_filename': plot_filename,
            'total_time': total_time,
            'analysis': comparison_analysis
        }
    else:
        print("処理できるデータがありませんでした。")
        return None

def analyze_comparison_results(mip_results, sa_results):
    """MIPとSAの結果を詳細分析する関数"""
    if not mip_results or not sa_results:
        return None
    
    analysis = {}
    
    # データファイル別の比較
    file_comparison = []
    
    for mip_result in mip_results:
        file_name = mip_result['データファイル']
        sa_result = next((r for r in sa_results if r['データファイル'] == file_name), None)
        
        if sa_result:
            cost_diff = sa_result['総コスト'] - mip_result['総コスト']
            time_diff = sa_result['計算時間'] - mip_result['計算時間']
            cost_ratio = sa_result['総コスト'] / mip_result['総コスト'] if mip_result['総コスト'] > 0 else 1
            
            file_comparison.append({
                'データファイル': file_name,
                'MIP総コスト': mip_result['総コスト'],
                'SA総コスト': sa_result['総コスト'],
                'コスト差': cost_diff,
                'コスト比': cost_ratio,
                'MIP計算時間': mip_result['計算時間'],
                'SA計算時間': sa_result['計算時間'],
                '時間差': time_diff,
                'MIPが優秀': cost_diff > 0
            })
    
    if file_comparison:
        comparison_df = pd.DataFrame(file_comparison)
        
        # 統計情報
        analysis['ファイル数'] = len(file_comparison)
        analysis['MIP勝利数'] = sum(comparison_df['MIPが優秀'])
        analysis['SA勝利数'] = len(file_comparison) - analysis['MIP勝利数']
        analysis['平均コスト差'] = comparison_df['コスト差'].mean()
        analysis['平均コスト比'] = comparison_df['コスト比'].mean()
        analysis['平均時間差'] = comparison_df['時間差'].mean()
        
        # 比較結果を保存
        save_results_to_csv(file_comparison, 'Detailed_comparison.csv')
        
        print(f"\n=== 詳細比較分析 ===")
        print(f"比較ファイル数: {analysis['ファイル数']}")
        print(f"MIP勝利: {analysis['MIP勝利数']}ファイル")
        print(f"SA勝利: {analysis['SA勝利数']}ファイル")
        print(f"平均コスト差 (SA - MIP): {analysis['平均コスト差']:.2f}")
        print(f"平均コスト比 (SA / MIP): {analysis['平均コスト比']:.3f}")
        print(f"平均時間差 (SA - MIP): {analysis['平均時間差']:.2f}秒")
    
    return analysis

def main():
    """メイン実行関数"""
    print("=" * 60)
    print("MIP vs 多スタート焼きなまし法 比較実行")
    print("=" * 60)
    
    # データフォルダの確認
    data_folder = 'data'
    if not os.path.exists(data_folder):
        print(f"データフォルダ '{data_folder}' が見つかりません。")
        return
    
    # パラメータ設定
    mip_time_limit = 500
    sa_params = {
        'num_starts': 5,
        'max_iterations': 1000,
        'initial_temp': 1000,
        'cooling_rate': 0.95
    }
    
    print(f"MIP時間制限: {mip_time_limit}秒")
    print(f"SAパラメータ: {sa_params}")
    
    # 比較実行
    result = run_comparison_batch(data_folder, mip_time_limit, sa_params)
    
    if result:
        print(f"\n=== 最終結果サマリー ===")
        print(f"総処理時間: {result['total_time']:.2f}秒")
        print(f"全結果CSV: {result['all_csv']}")
        print(f"比較CSV: {result['comparison_csv']}")
        print(f"統計CSV: {result['stats_csv']}")
        print(f"比較プロット: {result['plot_filename']}")
        
        if result['analysis']:
            analysis = result['analysis']
            print(f"\nMIP勝利率: {analysis['MIP勝利数'] / analysis['ファイル数'] * 100:.1f}%")
            print(f"SA勝利率: {analysis['SA勝利数'] / analysis['ファイル数'] * 100:.1f}%")
    else:
        print("比較処理に失敗しました。")

if __name__ == "__main__":
    main()
