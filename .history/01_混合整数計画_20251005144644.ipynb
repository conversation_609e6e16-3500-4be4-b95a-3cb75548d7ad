{"cells": [{"cell_type": "code", "execution_count": null, "id": "f57a500f", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["見つかったCSVファイル: ['D28.csv', 'D36.csv', 'D42.csv', 'D40.csv']\n", "\n", "=== Processing data/D28.csv ===\n", "\n", "=== 初期在庫水準の調整アルゴリズム開始 ===\n", "--- 調整イテレーション 1 ---\n", "  今回の調整量: [298.0, 163.0, 366.0, 307.20000000000005, 94.0, 527.0, 12.8, 670.0, 84.0, 16.799999999999997, 100.80000000000001, 222.0, 281.6]\n", "  更新後の初期在庫量: [613.0, 599.0, 1014.0, 1173.8, 209.0, 1713.0, 25.2, 1369.0, 336.0, 40.2, 281.2, 592.0, 950.4]\n", "--- 調整イテレーション 2 ---\n", "  今回の調整量: [298.0, 163.0, 366.0, 307.20000000000005, 94.0, 527.0, 12.8, 670.0, 84.0, 16.8, 100.80000000000001, 222.0, 281.6]\n", "  更新後の初期在庫量: [315.0, 436.0, 648.0, 866.5999999999999, 115.0, 1186.0, 12.399999999999999, 699.0, 252.0, 23.400000000000002, 180.39999999999998, 370.0, 668.8]\n", "--- 調整イテレーション 3 ---\n", "  今回の調整量: [298.0, 163.0, 366.0, 307.20000000000005, 94.0, 527.0, 12.399999999999999, 670.0, 84.0, 16.8, 100.8, 222.0, 281.6]\n", "  更新後の初期在庫量: [17.0, 273.0, 282.0, 559.3999999999999, 21.0, 659.0, 0.0, 29.0, 168.0, 6.600000000000001, 79.59999999999998, 148.0, 387.19999999999993]\n", "--- 最大反復回数に到達しました。---\n", "=== 混合整数計画法 スケジューリング ===\n", "Restricted license - for non-production use only - expires 2026-11-23\n", "Set parameter TimeLimit to value 100\n", "Gurobi Optimizer version 12.0.2 build v12.0.2rc0 (mac64[arm] - Darwin 24.6.0 24G90)\n", "\n", "CPU model: Apple M1\n", "Thread count: 8 physical cores, 8 logical processors, using up to 8 threads\n", "\n", "Non-default parameters:\n", "TimeLimit  100\n", "\n", "Optimize a model with 620 rows, 1080 columns and 2454 nonzeros\n", "Model fingerprint: 0x6f52b14e\n", "Variable types: 560 continuous, 520 integer (0 binary)\n", "Coefficient statistics:\n", "  Matrix range     [3e-01, 1e+06]\n", "  Objective range  [2e+01, 5e+02]\n", "  Bounds range     [1e+00, 1e+00]\n", "  RHS range        [1e+01, 1e+03]\n", "Found heuristic solution: objective 3.040902e+08\n", "Presolve removed 86 rows and 45 columns\n", "Presolve time: 0.01s\n", "Presolved: 534 rows, 1035 columns, 2297 nonzeros\n", "Variable types: 515 continuous, 520 integer (260 binary)\n", "\n", "Root relaxation: objective 3.402706e+04, 530 iterations, 0.00 seconds (0.00 work units)\n", "\n", "    Nodes    |    Current Node    |     Objective Bounds      |     Work\n", " Expl Unexpl |  Obj  Depth IntInf | Incumbent    BestBd   Gap | It/Node Time\n", "\n", "     0     0 34027.0585    0  352 3.0409e+08 34027.0585   100%     -    0s\n", "H    0     0                    6351997.4682 34027.0585  99.5%     -    0s\n", "H    0     0                    6351982.4682 34027.0585  99.5%     -    0s\n", "     0     0 429521.183    0  208 6351982.47 429521.183  93.2%     -    0s\n", "H    0     0                    4521076.5027 430366.245  90.5%     -    0s\n", "H    0     0                    4519876.5027 430366.245  90.5%     -    0s\n", "     0     0 430366.245    0  211 4519876.50 430366.245  90.5%     -    0s\n", "     0     0 470446.900    0  292 4519876.50 470446.900  89.6%     -    0s\n", "     0     0 472470.361    0  301 4519876.50 472470.361  89.5%     -    0s\n", "     0     0 472653.061    0  273 4519876.50 472653.061  89.5%     -    0s\n", "     0     0 483416.235    0  231 4519876.50 483416.235  89.3%     -    0s\n", "     0     0 485009.874    0  228 4519876.50 485009.874  89.3%     -    0s\n", "     0     0 485520.551    0  253 4519876.50 485520.551  89.3%     -    0s\n", "     0     0 489077.401    0  251 4519876.50 489077.401  89.2%     -    0s\n", "     0     0 489123.269    0  251 4519876.50 489123.269  89.2%     -    0s\n", "H    0     0                    4497994.7515 489123.269  89.1%     -    0s\n", "H    0     0                    4492193.5755 489123.269  89.1%     -    0s\n", "H    0     2                    4277877.4335 489123.269  88.6%     -    0s\n", "     0     2 489123.269    0  251 4277877.43 489123.269  88.6%     -    0s\n", "H   26    60                    3937824.7339 491497.909  87.5%  36.7    0s\n", "H   30    60                    3850123.3195 491497.909  87.2%  33.7    0s\n", "H   59   100                    3847363.3195 491497.909  87.2%  24.1    0s\n", "H  274   325                    3839172.5862 491497.909  87.2%  15.3    0s\n", "H  281   325                    3719816.6833 491497.909  86.8%  15.3    0s\n", "H  324   397                    3719416.6833 491497.909  86.8%  15.0    0s\n", "H 1166  1281                    3413851.1793 491497.909  85.6%   9.0    0s\n", "H 1188  1281                    3285277.6832 491497.909  85.0%   9.0    0s\n", "H 1233  1281                    3071019.4660 491497.909  84.0%   8.9    0s\n", "H 1342  1405                    2507835.9250 491497.909  80.4%   8.5    0s\n", "H 1404  1682                    2507820.9250 491497.909  80.4%   8.5    0s\n", "H 2418  2555                    544905.16362 491497.909  9.80%   6.6    0s\n", "H 3634  3744                    544076.63632 491497.909  9.66%   5.3    0s\n", "H 4720  3759                    541769.56461 491497.909  9.28%   4.7    0s\n", "H 4724  4030                    541754.56461 491497.909  9.28%   4.7    0s\n", "H 7529  6442                    541720.09192 491497.909  9.27%   4.0    0s\n", "H 7529  6440                    541675.09192 491497.909  9.26%   4.0    0s\n", "H 7545  6128                    540725.51879 495912.267  8.29%   4.0    1s\n", "H 7548  5823                    540275.94567 496086.654  8.18%   4.0    1s\n", "H 7548  5531                    540260.94567 496086.654  8.18%   4.0    1s\n", "H 7553  5257                    540237.09192 496276.543  8.14%   4.0    1s\n", "H 7554  4995                    537459.09192 496404.612  7.64%   4.0    1s\n", "H 7554  4745                    537150.09192 496404.612  7.59%   4.0    1s\n", "H 7556  4508                    536670.51879 496463.762  7.49%   4.0    1s\n", "H 7556  4282                    536295.51879 496463.762  7.43%   4.0    1s\n", "H 7559  4069                    534467.98596 496489.347  7.11%   4.0    2s\n", "H 7559  3865                    530638.58885 496489.347  6.44%   4.0    2s\n", "H 7560  3673                    528923.38628 496496.578  6.13%   4.0    2s\n", "H 7602  3524                    528175.38628 496517.248  5.99%   4.5    2s\n", "H 7649  3394                    527709.85897 496517.248  5.91%   4.7    2s\n", "H 7656  3226                    527226.85897 496517.248  5.82%   4.7    2s\n", "H 7661  3066                    527001.85897 496517.248  5.78%   4.7    2s\n", "H 7803  3009                    526521.85897 496517.248  5.70%   5.2    2s\n", "H 7997  3014                    524966.53323 496517.248  5.42%   5.8    2s\n", "H 8023  2916                    524951.53323 496517.248  5.42%   5.9    2s\n", "H 9080  3427                    524931.07857 496517.248  5.41%   8.1    3s\n", "H 9087  3293                    523062.94032 496517.248  5.08%   8.1    3s\n", "H 9088  3237                    522472.94032 496517.248  4.97%   8.1    3s\n", "H 9089  3123                    521716.89423 496517.248  4.83%   8.1    3s\n", "H10217  3438                    521606.58971 496557.692  4.80%   9.1    3s\n", "H10772  3709                    521396.58974 496607.747  4.75%   9.4    3s\n", "H10831  3569                    520509.07586 496607.747  4.59%   9.4    3s\n", "H11977  4129                    520393.07586 496607.747  4.57%   9.7    4s\n", "H13873  5639                    520363.07586 496609.929  4.56%  10.5    4s\n", "H13889  5637                    520333.07586 496609.929  4.56%  10.6    4s\n", "H13912  5633                    520303.07586 496609.929  4.55%  10.6    4s\n", "H15244  6739                    520258.07586 496702.287  4.53%  11.3    4s\n", "H15248  6738                    520243.07586 496702.287  4.52%  11.3    4s\n", " 15252  6972 499838.386   40  121 520243.076 496702.287  4.52%  11.3    5s\n", "H15569  7235                    520228.07586 496705.789  4.52%  11.5    5s\n", "H15619  7227                    520183.07586 496705.789  4.51%  11.5    5s\n", "H15762  7224                    520168.07586 496705.789  4.51%  11.6    5s\n", "H18033  9104                    520153.07586 496792.150  4.49%  12.6    5s\n", "H20078 10695                    520143.63976 496885.002  4.47%  12.9    6s\n", "H20806 11366                    520128.63976 496885.002  4.47%  12.9    6s\n", "H22776 12871                    519933.63976 496923.110  4.43%  13.3    7s\n", "H25489 14000                    518748.63976 496945.530  4.20%  13.2    7s\n", "H30078 18013                    518703.63976 496979.686  4.19%  13.8   11s\n", "H30078 18007                    518688.63976 496979.686  4.19%  13.8   12s\n", "H30100 17120                    518284.08433 496979.686  4.11%  13.8   13s\n", "H30100 16264                    518283.63976 496979.686  4.11%  13.8   13s\n", "H30102 15451                    517760.46226 496979.686  4.01%  13.8   14s\n", " 30108 15455 517760.462   49  285 517760.462 496979.686  4.01%  13.8   15s\n", "H30110 14684                    517380.58976 496979.686  3.94%  13.8   15s\n", "H30110 13949                    517320.58976 496979.686  3.93%  13.8   15s\n", "H30110 13251                    517275.58976 496979.686  3.92%  13.8   15s\n", "H30110 12588                    517251.01664 496979.686  3.92%  13.8   15s\n", "H30110 11958                    517215.58976 496979.686  3.91%  13.8   15s\n", "H30386 11551                    517173.40197 496979.686  3.90%  14.3   16s\n", "H30588 11109                    517142.41421 496979.686  3.90%  14.6   16s\n", "H31611 11133                    517022.41421 496979.686  3.88%  15.1   17s\n", "H32122 10860                    516962.41421 496979.686  3.87%  15.2   17s\n", "H33143 10919                    516812.41421 496979.686  3.84%  15.2   17s\n", "H34256 11126                    516797.41421 496979.686  3.83%  15.1   18s\n", "H35616 11368                    516742.71868 496979.686  3.82%  14.9   18s\n", "H35657 10887                    516606.74264 496979.686  3.80%  14.9   18s\n", "H35659 10491                    516591.74264 496979.686  3.80%  14.9   18s\n", "H35660  9834                    516439.15969 496979.686  3.77%  14.9   18s\n", "H38170  9159                    515674.15969 496979.686  3.63%  15.0   19s\n", "H40205  9973                    515569.15969 496979.686  3.61%  15.3   20s\n", "H41511 10263                    515554.15969 496979.686  3.60%  15.7   20s\n", "H43454 10917                    515551.66823 497045.918  3.59%  15.9   21s\n", "H43455 10616                    515517.95743 497045.918  3.58%  15.9   21s\n", "H43520 10299                    515454.61435 497045.918  3.57%  16.0   21s\n", "H45330 11100                    515403.81913 497103.225  3.55%  16.1   21s\n", "H50531 15746                    515328.81909 497177.163  3.52%  16.8   23s\n", "H52566 17049                    515298.81910 497191.929  3.51%  17.0   24s\n", "H52590 16419                    514475.20219 497191.929  3.36%  17.0   24s\n", " 56366 19800 514449.981  127   88 514475.202 497238.161  3.35%  17.4   25s\n", "H56543 19769                    514445.29669 497238.161  3.34%  17.4   25s\n", "H60090 22823                    514445.20221 497267.554  3.34%  18.0   26s\n", " 73387 33739 498907.216   36  164 514445.202 497384.247  3.32%  18.8   30s\n", "H81061 36601                    513505.82524 497419.848  3.13%  19.1   32s\n", " 91189 45392 507685.159   77  135 513505.825 497456.979  3.13%  19.3   35s\n", " 113460 63628 498814.307   33  205 513505.825 497586.469  3.10%  19.7   40s\n", "H122571 69953                    513498.08433 497625.682  3.09%  19.8   42s\n", "H127339 73673                    513468.08433 497638.517  3.08%  19.9   44s\n", "H127340 73654                    513461.87762 497638.517  3.08%  19.9   44s\n", " 130558 76573 512180.978   51  153 513461.878 497648.768  3.08%  20.0   45s\n", " 150375 92684 500906.971   51  145 513461.878 497716.134  3.07%  20.3   50s\n", "H161417 100851                    513431.87762 497739.084  3.06%  20.4   52s\n", " 170620 107941 501233.835   56  170 513431.878 497771.138  3.05%  20.4   55s\n", "H170626 107884                    513416.87762 497771.138  3.05%  20.4   55s\n", "H184864 114106                    512450.86769 497799.529  2.86%  20.2   58s\n", "H184871 114023                    512441.19452 497799.529  2.86%  20.2   58s\n", "H185616 114401                    512426.19452 497800.325  2.85%  20.2   58s\n", "H187037 115224                    512405.72273 497802.624  2.85%  20.2   58s\n", "H187556 115737                    512390.72273 497802.624  2.85%  20.2   59s\n", "H188818 116151                    512356.14990 497804.212  2.84%  20.2   59s\n", "H188922 116129                    512355.21425 497804.659  2.84%  20.2   59s\n", "H188955 114453                    512215.58504 497804.659  2.81%  20.2   59s\n", "H189075 114114                    512185.58504 497804.659  2.81%  20.2   59s\n", "H189321 114283                    512170.58504 497804.659  2.80%  20.2   59s\n", " 190587 115423 498892.439   37  187 512170.585 497809.875  2.80%  20.2   60s\n", "H190745 115391                    512166.45590 497809.875  2.80%  20.2   60s\n", "H190828 115240                    512151.45590 497810.001  2.80%  20.2   60s\n", "H190936 115223                    512148.94107 497811.369  2.80%  20.2   60s\n", "H191202 115149                    512140.58504 497813.327  2.80%  20.2   60s\n", "H191255 115535                    512125.58504 497813.327  2.79%  20.2   60s\n", "H192683 116710                    512121.45590 497817.439  2.79%  20.3   60s\n", "H193494 117273                    512106.45590 497819.052  2.79%  20.3   60s\n", "H195078 115498                    511844.95583 497823.941  2.74%  20.2   61s\n", "H196552 116525                    511749.65136 497826.943  2.72%  20.2   62s\n", " 208938 126476 498504.391   34  158 511749.651 497855.424  2.72%  20.3   65s\n", " 229401 142558 501037.053   51  149 511749.651 497894.640  2.71%  20.6   70s\n", " 249773 158493 498193.783   41  141 511749.651 497929.051  2.70%  20.8   75s\n", "H265551 169389                    511603.65136 497952.647  2.67%  20.7   78s\n", "H268001 169460                    511435.32915 497955.610  2.64%  20.7   79s\n", "H269695 171146                    511434.11558 497956.157  2.64%  20.8   79s\n", "H270191 170637                    511389.11558 497956.157  2.63%  20.8   79s\n", " 270335 171260 501534.108   55  162 511389.116 497957.093  2.63%  20.8   80s\n", "H272187 172585                    511374.11558 497959.593  2.62%  20.8   80s\n", "H285487 182333                    511359.11558 497978.915  2.62%  20.9   84s\n", " 288903 185477 499583.209   37  173 511359.116 497984.958  2.62%  20.9   85s\n", " 307268 199533     cutoff   47      511359.116 498011.670  2.61%  21.1   90s\n", " 326401 214702 505491.731   76  136 511359.116 498036.962  2.61%  21.2   95s\n", " 346845 230877 499041.722   39  177 511359.116 498059.775  2.60%  21.2  100s\n", "\n", "Cutting planes:\n", "  Learned: 2\n", "  Gomory: 19\n", "  Implied bound: 73\n", "  MIR: 231\n", "  Flow cover: 432\n", "  Flow path: 256\n", "  Relax-and-lift: 2\n", "\n", "Explored 347528 nodes (7376056 simplex iterations) in 100.01 seconds (157.63 work units)\n", "Thread count was 8 (of 8 available processors)\n", "\n", "Solution count 10: 511359 511374 511389 ... 512126\n", "\n", "Time limit reached\n", "Best objective 5.113591155759e+05, best bound 4.980608468368e+05, gap 2.6006%\n", "Gurobi status= 9\n", "ステータス: Not Solved\n", "\n", "解が見つかりませんでした\n", "\n", "=== Processing data/D36.csv ===\n", "\n", "=== 初期在庫水準の調整アルゴリズム開始 ===\n", "--- 調整イテレーション 1 ---\n", "  今回の調整量: [180.0, 387.0, 34.0, 46.8, 551.0, 1243.1999999999998]\n", "  更新後の初期在庫量: [437.0, 1030.0, 94.0, 115.2, 1584.0, 3163.8]\n", "--- 調整イテレーション 2 ---\n", "  今回の調整量: [180.0, 387.0, 34.0, 46.8, 551.0, 1243.2]\n", "  更新後の初期在庫量: [257.0, 643.0, 60.0, 68.4, 1033.0, 1920.6000000000001]\n", "--- 調整イテレーション 3 ---\n", "  今回の調整量: [180.0, 387.0, 34.0, 46.8, 551.0, 1243.2]\n", "  更新後の初期在庫量: [77.0, 256.0, 26.0, 21.60000000000001, 482.0, 677.4000000000001]\n", "--- 最大反復回数に到達しました。---\n", "=== 混合整数計画法 スケジューリング ===\n", "Set parameter TimeLimit to value 100\n", "Gurobi Optimizer version 12.0.2 build v12.0.2rc0 (mac64[arm] - Darwin 24.6.0 24G90)\n", "\n", "CPU model: Apple M1\n", "Thread count: 8 physical cores, 8 logical processors, using up to 8 threads\n", "\n", "Non-default parameters:\n", "TimeLimit  100\n", "\n", "Optimize a model with 340 rows, 520 columns and 1208 nonzeros\n", "Model fingerprint: 0xe7f2f990\n", "Variable types: 280 continuous, 240 integer (0 binary)\n", "Coefficient statistics:\n", "  Matrix range     [3e-01, 1e+06]\n", "  Objective range  [2e+01, 5e+02]\n", "  Bounds range     [1e+00, 1e+00]\n", "  RHS range        [8e+00, 1e+03]\n", "Found heuristic solution: objective 2.410100e+08\n", "Presolve removed 80 rows and 26 columns\n", "Presolve time: 0.00s\n", "Presolved: 260 rows, 494 columns, 1082 nonzeros\n", "Variable types: 254 continuous, 240 integer (120 binary)\n", "\n", "Root relaxation: objective 5.062019e+03, 240 iterations, 0.00 seconds (0.00 work units)\n", "\n", "    Nodes    |    Current Node    |     Objective Bounds      |     Work\n", " Expl Unexpl |  Obj  Depth IntInf | Incumbent    BestBd   Gap | It/Node Time\n", "\n", "     0     0 5062.01852    0  160 2.4101e+08 5062.01852   100%     -    0s\n", "H    0     0                    79896.000000 5062.01852  93.7%     -    0s\n", "H    0     0                    78572.000000 6193.21633  92.1%     -    0s\n", "     0     0 48070.9997    0   72 78572.0000 48070.9997  38.8%     -    0s\n", "H    0     0                    65628.000000 48077.8373  26.7%     -    0s\n", "H    0     0                    51047.000000 48077.8373  5.82%     -    0s\n", "H    0     0                    49650.000000 48077.8373  3.17%     -    0s\n", "     0     0 48114.8068    0   57 49650.0000 48114.8068  3.09%     -    0s\n", "     0     0 48116.0204    0   38 49650.0000 48116.0204  3.09%     -    0s\n", "H    0     0                    48727.000000 48117.0065  1.25%     -    0s\n", "     0     0 48137.2708    0   36 48727.0000 48137.2708  1.21%     -    0s\n", "     0     0 48138.9297    0   36 48727.0000 48138.9297  1.21%     -    0s\n", "H    0     0                    48568.000000 48138.9297  0.88%     -    0s\n", "     0     0 48138.9297    0   78 48568.0000 48138.9297  0.88%     -    0s\n", "     0     0 48138.9297    0   35 48568.0000 48138.9297  0.88%     -    0s\n", "H    0     0                    48448.000000 48138.9297  0.64%     -    0s\n", "     0     0 48138.9297    0   36 48448.0000 48138.9297  0.64%     -    0s\n", "     0     0 48138.9297    0   38 48448.0000 48138.9297  0.64%     -    0s\n", "     0     0 48139.0429    0   38 48448.0000 48139.0429  0.64%     -    0s\n", "H    0     0                    48403.000000 48139.8319  0.54%     -    0s\n", "     0     2 48139.8319    0   37 48403.0000 48139.8319  0.54%     -    0s\n", "H  199   214                    48388.000000 48155.9349  0.48%   1.4    0s\n", "H  204   214                    48358.000000 48155.9349  0.42%   1.4    0s\n", "*  909   893              44    48330.000000 48155.9349  0.36%   1.6    0s\n", "H 1008   894                    48300.000000 48162.3851  0.28%   1.6    0s\n", "H 1217   821                    48285.000000 48165.2546  0.25%   1.6    0s\n", "H 1219   719                    48270.000000 48165.2546  0.22%   1.6    0s\n", "H 1250   621                    48255.000000 48165.2546  0.19%   1.6    0s\n", "H 1945   952                    48240.000000 48172.0159  0.14%   1.7    0s\n", "\n", "Cutting planes:\n", "  Gomory: 8\n", "  MIR: 12\n", "\n", "Explored 2052 nodes (4683 simplex iterations) in 0.51 seconds (0.29 work units)\n", "Thread count was 8 (of 8 available processors)\n", "\n", "Solution count 10: 48240 48255 48270 ... 48448\n", "\n", "Optimal solution found (tolerance 1.00e-04)\n", "Best objective 4.824000000000e+04, best bound 4.823528979089e+04, gap 0.0098%\n", "Gurobi status= 2\n", "ステータス: Optimal\n", "総コスト: 48240.0\n", "\n", "=== 最適化結果 ===\n", "最良個体の総コスト: 48240.00\n", "計算時間: 0.53秒\n", "結果をCSVファイルに保存: result//MIP_results_D36.csv\n", "\n", "=== 結果のプロット ===\n", "プロットを保存: result//MIP_results_D36.png\n", "時間制約違反: 0 期間\n", "\n", "=== Processing data/D42.csv ===\n", "\n", "=== 初期在庫水準の調整アルゴリズム開始 ===\n", "--- 調整イテレーション 1 ---\n", "  今回の調整量: [121.14999999999998, 240.0, 60.0, 75.0, 198.0, 57.599999999999994, 124.60000000000002, 411.04999999999995, 105.19999999999999, 54.7, 806.4000000000001, 201.60000000000002, 222.0]\n", "  更新後の初期在庫量: [362.85, 621.0, 168.0, 213.0, 669.0, 176.4, 389.4, 1255.95, 338.8, 112.3, 3199.6, 473.4, 498.0]\n", "--- 調整イテレーション 2 ---\n", "  今回の調整量: [121.15, 240.0, 60.0, 75.0, 198.0, 57.599999999999994, 124.60000000000002, 411.04999999999995, 105.19999999999999, 54.7, 806.4000000000001, 201.60000000000002, 222.0]\n", "  更新後の初期在庫量: [241.70000000000002, 381.0, 108.0, 138.0, 471.0, 118.80000000000001, 264.79999999999995, 844.9000000000001, 233.60000000000002, 57.599999999999994, 2393.2, 271.79999999999995, 276.0]\n", "--- 調整イテレーション 3 ---\n", "  今回の調整量: [121.15, 240.0, 60.0, 75.0, 198.0, 57.6, 124.6, 411.05, 105.19999999999999, 54.7, 806.4000000000001, 201.6, 222.0]\n", "  更新後の初期在庫量: [120.55000000000001, 141.0, 48.0, 63.0, 273.0, 61.20000000000001, 140.19999999999996, 433.8500000000001, 128.40000000000003, 2.8999999999999915, 1586.7999999999997, 70.19999999999996, 54.0]\n", "--- 最大反復回数に到達しました。---\n", "=== 混合整数計画法 スケジューリング ===\n", "Set parameter TimeLimit to value 100\n", "Gurobi Optimizer version 12.0.2 build v12.0.2rc0 (mac64[arm] - Darwin 24.6.0 24G90)\n", "\n", "CPU model: Apple M1\n", "Thread count: 8 physical cores, 8 logical processors, using up to 8 threads\n", "\n", "Non-default parameters:\n", "TimeLimit  100\n", "\n", "Optimize a model with 620 rows, 1080 columns and 2454 nonzeros\n", "Model fingerprint: 0x22902d6d\n", "Variable types: 560 continuous, 520 integer (0 binary)\n", "Coefficient statistics:\n", "  Matrix range     [3e-01, 1e+06]\n", "  Objective range  [2e+01, 5e+02]\n", "  Bounds range     [1e+00, 1e+00]\n", "  RHS range        [6e-01, 1e+03]\n", "Found heuristic solution: objective 2.503596e+08\n", "Presolve removed 86 rows and 45 columns\n", "Presolve time: 0.01s\n", "Presolved: 534 rows, 1035 columns, 2297 nonzeros\n", "Variable types: 515 continuous, 520 integer (260 binary)\n", "\n", "Root relaxation: objective 1.925905e+04, 508 iterations, 0.00 seconds (0.00 work units)\n", "\n", "    Nodes    |    Current Node    |     Objective Bounds      |     Work\n", " Expl Unexpl |  Obj  Depth IntInf | Incumbent    BestBd   Gap | It/Node Time\n", "\n", "     0     0 19259.0471    0  405 2.5036e+08 19259.0471   100%     -    0s\n", "H    0     0                    1.090245e+07 19259.0471   100%     -    0s\n", "H    0     0                    1.089843e+07 19259.0471   100%     -    0s\n", "H    0     0                    7326263.6360 20529.1468   100%     -    0s\n", "H    0     0                    7324633.6360 20529.1468   100%     -    0s\n", "     0     0 296051.068    0  297 7324633.64 296051.068  96.0%     -    0s\n", "H    0     0                    1657266.5806 296544.456  82.1%     -    0s\n", "H    0     0                    1656466.5806 296544.456  82.1%     -    0s\n", "     0     0 352605.166    0  306 1656466.58 352605.166  78.7%     -    0s\n", "H    0     0                    1411704.8558 353958.093  74.9%     -    0s\n", "     0     0 353958.093    0  282 1411704.86 353958.093  74.9%     -    0s\n", "     0     0 354255.201    0  278 1411704.86 354255.201  74.9%     -    0s\n", "     0     0 361211.744    0  288 1411704.86 361211.744  74.4%     -    0s\n", "     0     0 361490.718    0  302 1411704.86 361490.718  74.4%     -    0s\n", "     0     0 362121.808    0  315 1411704.86 362121.808  74.3%     -    0s\n", "     0     0 362125.082    0  312 1411704.86 362125.082  74.3%     -    0s\n", "H    0     0                    473257.39820 362125.082  23.5%     -    0s\n", "H    0     0                    471367.35117 362125.082  23.2%     -    0s\n", "H    0     0                    471322.35117 362125.082  23.2%     -    0s\n", "H    0     0                    471274.37823 362125.082  23.2%     -    0s\n", "H    0     2                    469135.37823 362125.082  22.8%     -    0s\n", "     0     2 362125.082    0  312 469135.378 362125.082  22.8%     -    0s\n", "H   26    64                    469023.77894 362235.121  22.8%  14.0    0s\n", "H   31    64                    468171.22310 362235.121  22.6%  12.8    0s\n", "H   36    64                    466254.22310 362235.121  22.3%  13.0    0s\n", "H   56    64                    463652.95830 362235.121  21.9%  10.8    0s\n", "H  399   480                    463459.30317 362235.121  21.8%   7.1    0s\n", "H  409   480                    455892.95830 362235.121  20.5%   7.0    0s\n", "H  497   566                    394239.28519 362235.121  8.12%   6.5    0s\n", "H  509   566                    390447.55332 362235.121  7.23%   6.4    0s\n", "H  559   566                    381641.66917 362235.121  5.09%   6.1    0s\n", "H 2184  2413                    380793.65448 362235.121  4.87%   2.8    0s\n", "H 2229  2412                    378423.18990 362235.121  4.28%   2.7    0s\n", "H 2369  2359                    372657.19728 362235.121  2.80%   2.7    0s\n", "H 2413  2765                    372642.19728 362235.121  2.79%   2.7    0s\n", "H 6368  6295                    371963.19286 362235.121  2.62%   2.0    0s\n", "H 6374  5587                    370450.54076 362235.121  2.22%   2.0    0s\n", "H 7288  6151                    370223.04076 362238.758  2.16%   2.2    0s\n", "H 7297  5848                    370150.04076 362869.389  1.97%   2.2    1s\n", "H 7303  5559                    370107.04076 363313.959  1.84%   2.2    1s\n", "H 7309  5284                    369549.04076 363350.823  1.68%   2.2    1s\n", "H 7309  5020                    369212.04076 363350.823  1.59%   2.2    1s\n", "H 7310  4769                    369036.04076 363419.387  1.52%   2.2    1s\n", "H 7311  4531                    368699.04076 363425.660  1.43%   2.2    1s\n", "H 7311  4304                    368684.04076 363425.660  1.43%   2.2    1s\n", "H 7311  4089                    368662.54076 363425.660  1.42%   2.2    1s\n", "H 7311  3884                    368588.21751 363425.660  1.40%   2.2    1s\n", "H 7315  3691                    368580.59399 363449.776  1.39%   2.2    1s\n", "H 7315  3506                    368465.86822 363449.776  1.36%   2.2    1s\n", "H 7315  3330                    368458.39246 363449.776  1.36%   2.2    1s\n", "H 7315  3163                    368458.37151 363449.776  1.36%   2.2    1s\n", "H 7315  3005                    368430.81693 363449.776  1.35%   2.2    1s\n", "H 7315  2854                    368415.81693 363449.776  1.35%   2.2    1s\n", "H 7317  2713                    367918.04244 363451.097  1.21%   2.2    1s\n", "H 7318  2577                    367907.89515 363452.052  1.21%   2.2    1s\n", "H 7318  2447                    367849.99444 363452.052  1.20%   2.2    1s\n", "H 7319  2325                    367842.61797 363452.118  1.19%   2.1    1s\n", "H 7319  2209                    367730.44122 363452.118  1.16%   2.1    1s\n", "H 7319  2098                    367576.61797 363452.118  1.12%   2.1    1s\n", "H 7325  1996                    367232.82153 363479.450  1.02%   2.1    1s\n", "H 7325  1895                    367194.22224 363479.450  1.01%   2.1    1s\n", "H 7327  1801                    367174.89776 363495.761  1.00%   2.1    2s\n", "H 7327  1710                    367144.89776 363495.761  0.99%   2.1    2s\n", "H 7337  1631                    366864.18692 363509.709  0.91%   2.1    2s\n", "H 7337  1548                    366849.18692 363509.709  0.91%   2.1    2s\n", "H 7337  1470                    366212.66685 363509.709  0.74%   2.1    2s\n", "H 7337  1396                    366064.61885 363509.709  0.70%   2.1    2s\n", "H 7337  1325                    365863.76172 363509.709  0.64%   2.1    2s\n", "H 7338  1259                    365851.16685 363510.636  0.64%   2.1    2s\n", "H 7339  1195                    365812.56756 363514.007  0.63%   2.1    2s\n", "H 7339  1134                    365780.31756 363514.007  0.62%   2.1    2s\n", "H 7339  1077                    365776.06756 363514.007  0.62%   2.1    2s\n", "H 7339  1022                    365761.06756 363514.007  0.61%   2.1    2s\n", "H 7342   972                    365469.94508 363520.939  0.53%   2.1    2s\n", "H 7344   924                    365402.65506 363523.220  0.51%   2.1    3s\n", "H 7346   878                    365394.94508 363526.343  0.51%   2.1    3s\n", "H 7346   833                    365229.94508 363526.343  0.47%   2.1    3s\n", "H 7347   792                    365160.12183 363526.343  0.45%   2.1    3s\n", "H 7363   763                    365130.12183 363534.430  0.44%   2.5    3s\n", "H 7363   725                    365115.12183 363534.430  0.43%   2.5    3s\n", "H 7364   689                    365085.12183 363534.430  0.42%   2.5    3s\n", "H 7368   656                    365070.12183 363539.399  0.42%   2.5    4s\n", "H 7369   624                    365062.83181 363543.626  0.42%   2.5    4s\n", "  7378   630 365062.832  308  267 365062.832 363548.322  0.41%   2.4    5s\n", "H 7378   598                    365022.15628 363548.322  0.40%   2.4    5s\n", "H 7378   568                    364992.15628 363548.322  0.40%   2.4    5s\n", "H 7409   586                    364932.15628 363554.384  0.38%   2.7    5s\n", "H 7504   605                    364917.15628 363554.384  0.37%   2.9    5s\n", "H 7700   659                    364912.90628 363554.384  0.37%   3.1    5s\n", "H 8425   994                    364897.90628 363555.979  0.37%   3.7    5s\n", "H 8626  1038                    364886.00968 363555.979  0.36%   3.7    5s\n", "H 8629  1016                    364867.90628 363555.979  0.36%   3.7    5s\n", "H 9758  1875                    364827.15628 363564.068  0.35%   3.7    6s\n", "H22758 10316                    364812.15628 363658.689  0.32%   6.0    8s\n", " 29359 14147 363919.828   44  312 364812.156 363683.335  0.31%   6.6   11s\n", " 30411 14572 363731.712   50  216 364812.156 363698.785  0.31%   6.8   15s\n", "H60435 22264                    364773.69775 363841.346  0.26%   7.6   18s\n", "H60438 20808                    364743.69775 363841.346  0.25%   7.6   18s\n", "H61188 16769                    364641.97199 363842.992  0.22%   7.6   19s\n", "H61188 15028                    364599.56153 363842.992  0.21%   7.6   19s\n", "H61196 14223                    364584.56153 363842.992  0.20%   7.6   19s\n", "H61444 12045                    364508.23828 363844.418  0.18%   7.6   19s\n", "H61506 12027                    364507.51533 363844.418  0.18%   7.6   19s\n", " 66627 15003 364240.557   57  218 364507.515 363858.960  0.18%   7.8   20s\n", " 105516 31010 364421.182   69  173 364507.515 363941.158  0.16%   9.1   25s\n", "H106892 30806                    364496.76533 363943.010  0.15%   9.1   25s\n", "H107006 29196                    364466.76533 363943.055  0.14%   9.1   25s\n", "H108805 28316                    364436.76533 363946.364  0.13%   9.2   25s\n", "H108857 26566                    364406.76533 363946.364  0.13%   9.2   25s\n", "H110433 26861                    364405.61060 363949.385  0.13%   9.2   26s\n", "H110513 26122                    364393.08858 363949.385  0.12%   9.2   26s\n", "H111847 25650                    364378.08858 363951.818  0.12%   9.2   26s\n", "H120662 27602                    364376.76533 363968.145  0.11%   9.5   27s\n", "H122498 27273                    364365.47757 363971.443  0.11%   9.5   27s\n", " 135720 30125 364216.754   64  190 364365.478 363994.458  0.10%   9.9   30s\n", " 165621 34756     cutoff   77      364365.478 364035.716  0.09%  10.5   35s\n", " 198057 38202 364299.925   62  207 364365.478 364073.875  0.08%  10.9   40s\n", " 234425 42756 364267.085   64  186 364365.478 364107.096  0.07%  11.0   45s\n", " 271661 47841 364208.071   66  195 364365.478 364134.317  0.06%  11.0   50s\n", " 315300 55189 364254.263   60  218 364365.478 364159.204  0.06%  10.7   55s\n", " 359312 62119 364357.024  121   64 364365.478 364181.108  0.05%  10.5   60s\n", " 406232 68520 364307.225   60  209 364365.478 364202.954  0.04%  10.1   65s\n", " 449478 72982 364337.942  124   69 364365.478 364221.895  0.04%   9.9   70s\n", " 496125 74294 364334.102  125   55 364365.478 364243.940  0.03%   9.7   75s\n", " 542562 72774     cutoff  162      364365.478 364265.770  0.03%   9.5   80s\n", "H556666 70766                    364363.32650 364272.515  0.02%   9.4   81s\n", " 586452 70436 364326.082   65  218 364363.327 364284.854  0.02%   9.3   85s\n", " 635074 74662 364308.200   55  178 364363.327 364298.512  0.02%   9.0   90s\n", " 678881 84609     cutoff   62      364363.327 364306.055  0.02%   8.6   95s\n", " 738006 96476 364344.578  138   45 364363.327 364311.759  0.01%   8.2  100s\n", "\n", "Cutting planes:\n", "  Learned: 1\n", "  Gomory: 52\n", "  Cover: 26\n", "  Implied bound: 54\n", "  MIR: 359\n", "  StrongCG: 1\n", "  Flow cover: 319\n", "  Flow path: 71\n", "  Inf proof: 37\n", "  Network: 3\n", "  Relax-and-lift: 1\n", "\n", "Explored 738598 nodes (6082389 simplex iterations) in 100.01 seconds (109.63 work units)\n", "Thread count was 8 (of 8 available processors)\n", "\n", "Solution count 10: 364363 364365 364377 ... 364497\n", "\n", "Time limit reached\n", "Best objective 3.643633265000e+05, best bound 3.643118274152e+05, gap 0.0141%\n", "Gurobi status= 9\n", "ステータス: Not Solved\n", "\n", "解が見つかりませんでした\n", "\n", "=== Processing data/D40.csv ===\n", "\n", "=== 初期在庫水準の調整アルゴリズム開始 ===\n", "--- 調整イテレーション 1 ---\n", "  今回の調整量: [1599.8999999999996, 30.0, 336.0, 22.950000000000003, 194.39999999999998, 335.29999999999995, 109.85000000000002, 32.2, 117.0]\n", "  更新後の初期在庫量: [5630.1, 86.0, 916.0, 65.05, 549.6, 903.7, 346.15, 116.8, 462.0]\n", "--- 調整イテレーション 2 ---\n", "  今回の調整量: [1599.9, 30.0, 336.0, 22.950000000000003, 194.39999999999998, 335.29999999999995, 109.85, 32.2, 117.0]\n", "  更新後の初期在庫量: [4030.2000000000003, 56.0, 580.0, 42.099999999999994, 355.20000000000005, 568.4000000000001, 236.29999999999998, 84.6, 345.0]\n", "--- 調整イテレーション 3 ---\n", "  今回の調整量: [1599.9, 30.0, 336.0, 22.95, 194.4, 335.3, 109.85, 32.2, 117.0]\n", "  更新後の初期在庫量: [2430.3, 26.0, 244.0, 19.149999999999995, 160.80000000000004, 233.10000000000008, 126.44999999999999, 52.39999999999999, 228.0]\n", "--- 最大反復回数に到達しました。---\n", "=== 混合整数計画法 スケジューリング ===\n", "Set parameter TimeLimit to value 100\n", "Gurobi Optimizer version 12.0.2 build v12.0.2rc0 (mac64[arm] - Darwin 24.6.0 24G90)\n", "\n", "CPU model: Apple M1\n", "Thread count: 8 physical cores, 8 logical processors, using up to 8 threads\n", "\n", "Non-default parameters:\n", "TimeLimit  100\n", "\n", "Optimize a model with 460 rows, 760 columns and 1742 nonzeros\n", "Model fingerprint: 0xe5119ba8\n", "Variable types: 400 continuous, 360 integer (0 binary)\n", "Coefficient statistics:\n", "  Matrix range     [3e-01, 1e+06]\n", "  Objective range  [2e+01, 5e+02]\n", "  Bounds range     [1e+00, 1e+00]\n", "  RHS range        [4e+00, 2e+03]\n", "Found heuristic solution: objective 2.569498e+08\n", "Presolve removed 84 rows and 37 columns\n", "Presolve time: 0.00s\n", "Presolved: 376 rows, 723 columns, 1597 nonzeros\n", "Variable types: 363 continuous, 360 integer (180 binary)\n", "\n", "Root relaxation: objective 2.018846e+04, 352 iterations, 0.00 seconds (0.00 work units)\n", "\n", "    Nodes    |    Current Node    |     Objective Bounds      |     Work\n", " Expl Unexpl |  Obj  Depth IntInf | Incumbent    BestBd   Gap | It/Node Time\n", "\n", "     0     0 20188.4640    0  292 2.5695e+08 20188.4640   100%     -    0s\n", "H    0     0                    1905820.5332 20188.4640  98.9%     -    0s\n", "H    0     0                    1862013.9041 20188.4640  98.9%     -    0s\n", "     0     0 153262.600    0  232 1862013.90 153262.600  91.8%     -    0s\n", "H    0     0                    374757.24480 153870.003  58.9%     -    0s\n", "     0     0 153870.003    0  230 374757.245 153870.003  58.9%     -    0s\n", "     0     0 185504.219    0  263 374757.245 185504.219  50.5%     -    0s\n", "     0     0 186304.033    0  260 374757.245 186304.033  50.3%     -    0s\n", "     0     0 186569.327    0  263 374757.245 186569.327  50.2%     -    0s\n", "     0     0 186597.882    0  264 374757.245 186597.882  50.2%     -    0s\n", "     0     0 196944.120    0  248 374757.245 196944.120  47.4%     -    0s\n", "     0     0 197959.912    0  235 374757.245 197959.912  47.2%     -    0s\n", "     0     0 197982.727    0  233 374757.245 197982.727  47.2%     -    0s\n", "     0     0 199418.123    0  252 374757.245 199418.123  46.8%     -    0s\n", "     0     0 199463.190    0  249 374757.245 199463.190  46.8%     -    0s\n", "H    0     0                    280442.56161 199540.414  28.8%     -    0s\n", "H    0     0                    279627.56161 199540.414  28.6%     -    0s\n", "H    0     0                    279177.56161 199540.414  28.5%     -    0s\n", "H    0     0                    277312.67277 199540.414  28.0%     -    0s\n", "H    0     2                    277242.65751 199540.414  28.0%     -    0s\n", "     0     2 199540.414    0  249 277242.658 199540.414  28.0%     -    0s\n", "H   26    64                    276972.65751 200178.284  27.7%  21.6    0s\n", "H   31    64                    276706.08891 200178.284  27.7%  20.5    0s\n", "H   46    64                    272526.84501 200178.284  26.5%  17.7    0s\n", "H 1007  1211                    251113.60394 200178.284  20.3%   5.0    0s\n", "H 1059  1024                    208615.87418 200178.284  4.04%   4.8    0s\n", "H 1215  1217                    208525.87418 200178.284  4.00%   4.4    0s\n", "H 3494  3416                    208450.87418 200232.848  3.94%   2.7    0s\n", "H 3548  3280                    208284.46880 200232.848  3.87%   2.7    0s\n", "H 3598  3243                    208226.88944 200232.848  3.84%   2.7    0s\n", "H 4428  1366                    205558.95133 200232.848  2.59%   2.6    0s\n", "H 4592  1388                    205528.95133 200232.848  2.58%   2.6    0s\n", "H 5224  1612                    205415.20133 201900.568  1.71%   2.8    0s\n", "H 5224  1531                    205404.45133 202111.062  1.60%   2.8    0s\n", "H 5230  1458                    205270.30224 202267.391  1.46%   2.8    0s\n", "H 5235  1387                    205106.40072 202396.378  1.32%   2.7    0s\n", "H 5237  1319                    204909.44580 202518.325  1.17%   2.7    1s\n", "H 5241  1255                    204872.94580 202625.174  1.10%   2.7    1s\n", "H 5241  1192                    204864.99089 202625.174  1.09%   2.7    1s\n", "H 5257  1142                    204621.45284 203077.858  0.75%   2.7    1s\n", "H 5372  1180                    204587.85437 203077.858  0.74%   3.9    2s\n", "H 6127  1210                    204370.31246 203077.858  0.63%   5.5    2s\n", "H 6155  1173                    204340.31246 203077.858  0.62%   5.5    2s\n", "H 7204  1095                    204310.31246 203193.124  0.55%   6.8    2s\n", "H 8314  1284                    204293.06246 203315.147  0.48%   7.7    2s\n", "H 8718  1374                    204278.06246 203365.026  0.45%   8.0    2s\n", " 16596  3350 204234.265   73   59 204278.062 203760.454  0.25%  11.7    5s\n", "H63442 25500                    204277.16739 204014.198  0.13%   7.0    9s\n", "H64311 22203                    204240.66731 204015.475  0.11%   6.9    9s\n", "H64546 21358                    204234.16756 204015.475  0.11%   6.9    9s\n", "H66458 20096                    204219.16757 204018.787  0.10%   6.8    9s\n", " 66604 20455 204162.427   70   63 204219.168 204019.003  0.10%   6.8   10s\n", "*67409 19428             150    204212.22285 204020.240  0.09%   6.8   10s\n", "H67487 17524                    204197.22285 204020.240  0.09%   6.8   10s\n", "H76654 19982                    204197.03925 204036.040  0.08%   6.5   10s\n", "*80019 16848             125    204175.54381 204042.244  0.07%   6.4   10s\n", "*103835 18855             124    204175.54376 204093.239  0.04%   6.1   12s\n", "*109301 17556             133    204169.04395 204102.274  0.03%   6.1   13s\n", "H113334 16572                    204164.79384 204106.938  0.03%   6.0   13s\n", "*114790 10907             122    204149.79384 204108.395  0.02%   5.9   13s\n", " 128269 10977     cutoff   89      204149.794 204121.462  0.01%   5.7   15s\n", "*129276 10932             108    204149.79379 204121.883  0.01%   5.7   15s\n", "\n", "Cutting planes:\n", "  Gomory: 48\n", "  Cover: 1\n", "  Implied bound: 22\n", "  MIR: 350\n", "  Flow cover: 217\n", "  Flow path: 84\n", "  Inf proof: 12\n", "  Relax-and-lift: 3\n", "\n", "Explored 146613 nodes (803752 simplex iterations) in 16.55 seconds (16.01 work units)\n", "Thread count was 8 (of 8 available processors)\n", "\n", "Solution count 10: 204150 204150 204150 ... 204212\n", "\n", "Optimal solution found (tolerance 1.00e-04)\n", "Best objective 2.041497939697e+05, best bound 2.041295471077e+05, gap 0.0099%\n", "Gurobi status= 2\n", "ステータス: Optimal\n", "総コスト: 204149.79396969688\n", "\n", "=== 最適化結果 ===\n", "最良個体の総コスト: 204149.79\n", "計算時間: 16.63秒\n", "結果をCSVファイルに保存: result//MIP_results_D40.csv\n", "\n", "=== 結果のプロット ===\n", "プロットを保存: result//MIP_results_D40.png\n", "時間制約違反: 0 期間\n", "\n", "集計結果をCSVファイルに保存: result//MIP_aggregate_results.csv\n", "\n", "=== 全体の集計結果 ===\n", "処理したファイル数: 2\n", "総目的関数値: 252389.79\n", "総計算時間: 17.16秒\n", "平均目的関数値: 126194.90\n", "平均計算時間: 8.58秒\n"]}, {"data": {"image/png": "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", "text/plain": ["<Figure size 1600x1200 with 4 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 1600x1200 with 4 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 1500x600 with 2 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["import pandas as pd\n", "import pulp\n", "import csv\n", "import random\n", "import matplotlib.pyplot as plt\n", "import japanize_matplotlib\n", "import copy\n", "\n", "# グローバル変数\n", "品番リスト = []\n", "出荷数リスト = []\n", "収容数リスト = []\n", "サイクルタイムリスト = []\n", "込め数リスト = []\n", "初期在庫量リスト = []\n", "\n", "# コストとペナルティの係数\n", "在庫コスト単価 = 180\n", "残業コスト単価 = 66.7\n", "段替えコスト単価 = 400\n", "出荷遅れコスト単価 = 500\n", "\n", "定時 = 8 * 60 * 2\n", "最大残業時間 = 2 * 60 * 2\n", "段替え時間 = 30\n", "\n", "def read_csv(file_path):\n", "    \"\"\"CSVファイルを読み込む関数\"\"\"\n", "    global 品番リスト, 出荷数リスト, 収容数リスト, サイクルタイムリスト, 込め数リスト, 初期在庫量リスト\n", "    \n", "    収容数辞書 = {}\n", "    with open('収容数.csv', 'r', encoding='shift-jis') as capacity_file:\n", "        capacity_reader = csv.reader(capacity_file)\n", "        capacity_header = next(capacity_reader)\n", "        for row in capacity_reader:\n", "            if len(row) >= 2 and row[1].strip():  # Skip if second column is empty\n", "                品番 = row[0]  # 品番列\n", "                収容数 = int(float(row[1]))  # 収容数列\n", "                収容数辞書[品番] = 収容数\n", "\n", "    with open(file_path, 'r', encoding='shift-jis') as file:\n", "        reader = csv.reader(file)\n", "        header = next(reader)\n", "        \n", "        品番リスト = []\n", "        出荷数リスト = []\n", "        収容数リスト = []\n", "        サイクルタイムリスト = []\n", "        込め数リスト = []\n", "        \n", "        # 期間数（日数）を定義\n", "        期間数 = 20\n", "        \n", "        rows = list(reader)\n", "        for row in rows:\n", "            if len(row) == 0:\n", "                continue\n", "            \n", "            # 個数を取得\n", "            total_quantity = int(row[header.index(\"個数\")])\n", "            \n", "            # 個数が200未満の場合はスキップ\n", "            if total_quantity < 200:\n", "                continue\n", "            \n", "            # 1日あたりの出荷数を計算（総期間で割る）\n", "            daily_quantity = total_quantity / 期間数\n", "            \n", "            品番リスト.append(row[header.index(\"素材品番\")])\n", "            出荷数リスト.append(daily_quantity)\n", "            込め数リスト.append(int(float(row[header.index(\"込数\")])))\n", "            \n", "            cycle_time_per_unit = float(row[header.index(\"サイクルタイム\")]) / 60\n", "            サイクルタイムリスト.append(cycle_time_per_unit)\n", "            \n", "            収容数リスト.append(収容数辞書.get(品番, 80))\n", "            \n", "    \n", "        # 出荷数に基づいて初期在庫量（処理前）をランダムに設定\n", "        初期在庫量リスト = []\n", "        for shipment in 出荷数リスト:\n", "            random_inventory = random.randint(int(shipment * 3), int(shipment * 5))\n", "            初期在庫量リスト.append(random_inventory)\n", "            \n", "    return 品番リスト, 出荷数リスト, 収容数リスト, サイクルタイムリスト, 込め数リスト, 初期在庫量リスト\n", "\n", "def solve_mip(initial_inventory_list_arg):\n", "    \"\"\"PuLPを用いてMIPを解く関数\"\"\"\n", "    \n", "    # モデルの定義\n", "    model = pulp.LpProblem(\"ProductionScheduling\", pulp.LpMinimize)\n", "    \n", "    # インデックスの定義\n", "    品目 = range(品番数)\n", "    期間_index = range(期間)\n", "\n", "    # 決定変数\n", "    Production = pulp.LpVariable.dicts(\"Production\", (品目, 期間_index), lowBound=0, cat='Integer')\n", "    IsProduced = pulp.LpVariable.dicts(\"IsProduced\", (品目, 期間_index), cat='Binary')\n", "    Inventory = pulp.LpVariable.dicts(\"Inventory\", (品目, 期間_index), lowBound=0, cat='Continuous')\n", "    Shortage = pulp.LpVariable.dicts(\"Shortage\", (品目, 期間_index), lowBound=0, cat='Continuous')\n", "    WorkTime = pulp.LpVariable.dicts(\"WorkTime\", 期間_index, lowBound=0, cat='Continuous')\n", "    Overtime = pulp.LpVariable.dicts(\"Overtime\", 期間_index, lowBound=0, cat='Continuous')\n", "\n", "    # 目的関数\n", "    total_cost = pulp.lpSum(\n", "        在庫コスト単価 * Inventory[i][t]/収容数リスト[i] for i in 品目 for t in 期間_index\n", "    ) + pulp.lpSum(\n", "        残業コスト単価 * Overtime[t] for t in 期間_index\n", "    ) + pulp.lpSum(\n", "        段替えコスト単価 * IsProduced[i][t] for i in 品目 for t in 期間_index\n", "    ) + pulp.lpSum(\n", "        出荷遅れコスト単価 * Shortage[i][t] for i in 品目 for t in 期間_index\n", "    )\n", "    \n", "    model += total_cost, \"Total Cost\"\n", "\n", "    # 制約条件\n", "    bigM = 1000000\n", "\n", "    for i in 品目:\n", "        for t in 期間_index:\n", "            if t == 0:\n", "                # 初期在庫リストを使用\n", "                model += Inventory[i][t] - Shortage[i][t] == initial_inventory_list_arg[i] + Production[i][t] - 出荷数リスト[i]\n", "            else:\n", "                model += Inventory[i][t] - Shortage[i][t] == Inventory[i][t-1] - Shortage[i][t-1] + Production[i][t] - 出荷数リスト[i]\n", "            \n", "            model += Production[i][t] <= bigM * IsProduced[i][t]\n", "\n", "    for t in 期間_index:\n", "        model += WorkTime[t] == pulp.lpSum(\n", "            Production[i][t] * (サイクルタイムリスト[i] / 込め数リスト[i]) + 段替え時間 * IsProduced[i][t]\n", "            for i in 品目\n", "        )\n", "        \n", "        model += WorkTime[t] <= 定時 + Overtime[t]\n", "        model += WorkTime[t] <= 定時 + 最大残業時間\n", "        model += Overtime[t] >= WorkTime[t] - 定時\n", "        model += Overtime[t] >= 0\n", "\n", "    # Solverの設定\n", "    solver = pulp.GUROBI(msg=True, timelimit=500)\n", "    \n", "    # 最適化の実行\n", "    model.solve(solver)\n", "    \n", "    # ソルバーの詳細情報を取得\n", "    status = pulp.LpStatus[model.status]\n", "    print(\"ステータス:\", status)\n", "    \n", "    # Gurobiソルバーの詳細情報を取得\n", "    if hasattr(solver, 'solverModel') and solver.solverModel is not None:\n", "        gurobi_model = solver.solverModel\n", "        \n", "        # 最適性ギャップの取得\n", "        if hasattr(gurobi_model, 'MIPGap'):\n", "            gap = gurobi_model.MIPGap * 100  # パーセンテージに変換\n", "            print(f\"最適性ギャップ: {gap:.4f}%\")\n", "        \n", "        # 目的関数値と境界値の取得\n", "        if hasattr(gurobi_model, 'ObjVal'):\n", "            obj_val = gurobi_model.ObjVal\n", "            print(f\"目的関数値: {obj_val:.2f}\")\n", "            \n", "        if hasattr(gurobi_model, 'ObjBound'):\n", "            obj_bound = gurobi_model.ObjBound\n", "            print(f\"目的関数境界値: {obj_bound:.2f}\")\n", "            \n", "        # ソルバーステータスの詳細\n", "        if hasattr(gurobi_model, 'Status'):\n", "            gurobi_status = gurobi_model.Status\n", "            print(f\"Gurobiステータス: {gurobi_status}\")\n", "            \n", "            # ステータスの意味を表示\n", "            status_meanings = {\n", "                1: \"LOADED (モデルが読み込まれた)\",\n", "                2: \"OPTIMAL (最適解が見つかった)\",\n", "                3: \"INFEASIBLE (実行不可能)\",\n", "                4: \"INF_OR_UNBD (実行不可能または非有界)\",\n", "                5: \"UNBOUNDED (非有界)\",\n", "                6: \"CUTOFF (カットオフ値により終了)\",\n", "                7: \"ITERATION_LIMIT (反復回数制限により終了)\",\n", "                8: \"NODE_LIMIT (ノード数制限により終了)\",\n", "                9: \"TIME_LIMIT (時間制限により終了)\",\n", "                10: \"SOLUTION_LIMIT (解の数制限により終了)\",\n", "                11: \"INTERRUPTED (ユーザーにより中断)\",\n", "                12: \"NUMERIC (数値的困難)\",\n", "                13: \"SUBOPTIMAL (準最適解)\",\n", "                14: \"INPROGRESS (進行中)\",\n", "                15: \"USER_OBJ_LIMIT (ユーザー目的関数制限により終了)\"\n", "            }\n", "            if gurobi_status in status_meanings:\n", "                print(f\"ステータスの意味: {status_meanings[gurobi_status]}\")\n", "    \n", "    if status == 'Optimal':\n", "        print(\"総コスト:\", pulp.value(model.objective))\n", "\n", "        production_schedule = [[0] * 期間 for _ in range(品番数)]\n", "        for i in 品目:\n", "            for t in 期間_index:\n", "                production_schedule[i][t] = pulp.value(Production[i][t])\n", "\n", "        return production_schedule, pulp.value(model.objective)\n", "    elif status == 'Not Solved':\n", "        # 時間制限などで最適解が見つからなかった場合でも、実行可能解があれば取得\n", "        if pulp.value(model.objective) is not None:\n", "            print(\"時間制限により最適解は見つかりませんでしたが、実行可能解を取得しました\")\n", "            print(\"総コスト:\", pulp.value(model.objective))\n", "            \n", "            production_schedule = [[0] * 期間 for _ in range(品番数)]\n", "            for i in 品目:\n", "                for t in 期間_index:\n", "                    production_schedule[i][t] = pulp.value(Production[i][t])\n", "\n", "            return production_schedule, pulp.value(model.objective)\n", "    \n", "    return None, None\n", "\n", "\n", "def simulate_production_schedule(initial_inventory, 期間=20):\n", "    \"\"\"生産スケジュールをシミュレートする関数（MIP用の簡易版）\"\"\"\n", "    品番数 = len(initial_inventory)\n", "    inventory = initial_inventory[:]\n", "    inventory_history = [[] for _ in range(品番数)]\n", "    \n", "    daily_regular_time = 8 * 60 * 2  # 1日の通常稼働時間（分）\n", "    max_daily_overtime = 2 * 60 * 2  # 1日の最大残業時間（分）\n", "    max_daily_work_time = daily_regular_time + max_daily_overtime\n", "    \n", "    for t in range(期間):\n", "        daily_production_time = 0\n", "        daily_setup_count = 0\n", "        \n", "        # 各品番の需要を処理\n", "        for i in range(品番数):\n", "            demand = 出荷数リスト[i]\n", "            inventory[i] -= demand\n", "            \n", "            # 在庫が不足する場合は生産\n", "            if inventory[i] < 0:\n", "                shortage = abs(inventory[i])\n", "                # 生産量を決定（不足分を補う）\n", "                production = shortage\n", "                \n", "                # 生産時間を計算\n", "                if production > 0:\n", "                    daily_setup_count += 1\n", "                    setup_time = 30  # 段替え時間\n", "                    production_time = (production / 込め数リスト[i]) * サイクルタイムリスト[i]\n", "                    daily_production_time += production_time + setup_time\n", "                \n", "                inventory[i] += production\n", "            \n", "            # 在庫履歴に記録\n", "            inventory_history[i].append(max(0, inventory[i]))\n", "        \n", "        # 稼働時間制約チェック（制約違反の場合は一部生産を削減）\n", "        if daily_production_time > max_daily_work_time:\n", "            # 簡易的な調整：超過分を比例配分で削減\n", "            reduction_factor = max_daily_work_time / daily_production_time\n", "            for i in range(品番数):\n", "                if inventory[i] > 0:\n", "                    # 生産量を削減し、在庫を調整\n", "                    current_production = max(0, 出荷数リスト[i] - (initial_inventory[i] - inventory[i]))\n", "                    reduced_production = current_production * reduction_factor\n", "                    inventory[i] = initial_inventory[i] + reduced_production - 出荷数リスト[i] * (t + 1)\n", "                    inventory[i] = max(0, inventory[i])\n", "                    inventory_history[i][-1] = inventory[i]\n", "    \n", "    return inventory_history\n", "\n", "def adjust_initial_inventory(初期在庫量リスト, 在庫コスト単価, 出荷遅れコスト単価, num_simulations=50, max_iterations=3):\n", "    \"\"\"初期在庫を更新する関数（optimize_initial_inventoryと一致させた版）\"\"\"\n", "    \n", "    品番数 = len(初期在庫量リスト)\n", "    s = 初期在庫量リスト[:]\n", "    \n", "    # h / (h+c) - optimize_initial_inventoryと同じ計算方法\n", "    prob_target = 在庫コスト単価 / (在庫コスト単価 + 出荷遅れコスト単価)\n", "    \n", "    print(f\"\\n=== 初期在庫水準の調整アルゴリズム開始 ===\")\n", "    \n", "    for iteration in range(max_iterations):\n", "        print(f\"--- 調整イテレーション {iteration + 1} ---\")\n", "        \n", "        # 各在庫点について在庫量の分布を求める\n", "        inventory_distributions = [[] for _ in range(品番数)]\n", "        for _ in range(num_simulations):\n", "            inventory_history = simulate_production_schedule(s)\n", "            for i in range(品番数):\n", "                inventory_distributions[i].extend(inventory_history[i])\n", "        \n", "        adjustments = [0] * 品番数\n", "        \n", "        # 各在庫点について在庫量の最適調整量r^*を求める\n", "        for i in range(品番数):\n", "            if not inventory_distributions[i]:\n", "                continue\n", "            \n", "            inventory_counts = pd.Series(inventory_distributions[i]).value_counts(normalize=True).sort_index()\n", "            cumulative_distribution = inventory_counts.cumsum()\n", "            \n", "            # sum_{x <= r-1} f(x) <= h / (h + c) - optimize_initial_inventoryと同じロジック\n", "            best_r = 0\n", "            for r in cumulative_distribution.index:\n", "                prob_at_r_minus_1 = cumulative_distribution.get(r - 1, 0)\n", "                if prob_at_r_minus_1 <= prob_target:\n", "                    best_r = r\n", "                else:\n", "                    break\n", "            \n", "            adjustments[i] = s[i] - best_r\n", "            \n", "        print(f\"  今回の調整量: {adjustments}\")\n", "        \n", "        # すべての在庫点での初期在庫量をs:=s-r^*によって更新する\n", "        new_s = [s[i] - adjustments[i] for i in range(品番数)]\n", "        \n", "        # 終了条件のチェック - optimize_initial_inventoryと同じ条件\n", "        if all(abs(adj) < 1 for adj in adjustments):\n", "            print(\"--- アルゴリズムが収束しました。---\")\n", "            return s\n", "            \n", "        s = new_s\n", "        print(f\"  更新後の初期在庫量: {s}\")\n", "        \n", "    print(\"--- 最大反復回数に到達しました。---\")\n", "    return s\n", "\n", "def plot_results(best_individual, initial_inventory, save_path=None):\n", "    \"\"\"結果をプロットする関数\"\"\"\n", "    global 品番数, 期間, 品番リスト, 出荷数リスト, サイクルタイムリスト, 込め数リスト\n", "    \n", "    print(\"\\n=== 結果のプロット ===\")\n", "    \n", "    total_inventory_per_period = []\n", "    total_production_time_per_period = []\n", "    total_setup_times_per_period = []\n", "    total_shipment_delay_per_period = []\n", "\n", "    inventory = initial_inventory[:]\n", "    max_daily_work_time = (8 + 2) * 60 * 2\n", "    daily_regular_time = 8 * 60 * 2\n", "    \n", "    for t in range(期間):\n", "        daily_inventory = 0\n", "        daily_production_time = 0\n", "        daily_setup_times = 0\n", "        daily_shipment_delay = 0\n", "        \n", "        for i in range(品番数):\n", "            production = best_individual[i][t]\n", "            \n", "            if production > 0:\n", "                daily_setup_times += 1\n", "                setup_time = 30\n", "            else:\n", "                setup_time = 0\n", "\n", "            production_time = (production / 込め数リスト[i]) * サイクルタイムリスト[i]\n", "            daily_production_time += production_time + setup_time\n", "            \n", "            inventory[i] += production - 出荷数リスト[i]\n", "\n", "            if inventory[i] < 0:\n", "                daily_shipment_delay += abs(inventory[i])\n", "                inventory[i] = 0\n", "\n", "            daily_inventory += inventory[i]\n", "            \n", "        total_inventory_per_period.append(daily_inventory)\n", "        total_production_time_per_period.append(daily_production_time)\n", "        total_setup_times_per_period.append(daily_setup_times)\n", "        total_shipment_delay_per_period.append(daily_shipment_delay)\n", "\n", "    fig, axes = plt.subplots(2, 2, figsize=(16, 12))\n", "    periods = list(range(1, 期間 + 1))\n", "\n", "    # 1. 各期間の総在庫量\n", "    axes[0, 0].bar(periods, total_inventory_per_period, color='skyblue', alpha=0.7)\n", "    axes[0, 0].set_title('各期間の総在庫量')\n", "    axes[0, 0].set_xlabel('期間')\n", "    axes[0, 0].set_ylabel('総在庫量 (個)')\n", "    axes[0, 0].grid(True, alpha=0.3)\n", "    if total_inventory_per_period:\n", "        axes[0, 0].set_ylim(0, max(total_inventory_per_period) * 1.2)\n", "\n", "    # 2. 各期間の総生産時間＋制限ライン\n", "    axes[0, 1].bar(periods, total_production_time_per_period, color='lightgreen', alpha=0.7)\n", "    axes[0, 1].axhline(y=max_daily_work_time, color='red', linestyle='--', alpha=0.8, label=f'上限 ({max_daily_work_time}分)')\n", "    axes[0, 1].axhline(y=daily_regular_time, color='green', linestyle='--', alpha=0.8, label=f'定時 ({daily_regular_time}分)')\n", "    axes[0, 1].set_title('各期間の総生産時間')\n", "    axes[0, 1].set_xlabel('期間')\n", "    axes[0, 1].set_ylabel('総稼働時間 (分)')\n", "    axes[0, 1].grid(True, alpha=0.3)\n", "    if total_production_time_per_period:\n", "        axes[0, 1].set_ylim(0, max(max_daily_work_time, max(total_production_time_per_period)) * 1.2)\n", "    axes[0, 1].legend()\n", "\n", "    # 3. 各期間の総段替え回数\n", "    axes[1, 0].bar(periods, total_setup_times_per_period, color='salmon', alpha=0.7)\n", "    axes[1, 0].set_title('各期間の総段替え回数')\n", "    axes[1, 0].set_xlabel('期間')\n", "    axes[1, 0].set_ylabel('総段替え回数（回）')\n", "    axes[1, 0].grid(True, alpha=0.3)\n", "    if total_setup_times_per_period:\n", "        axes[1, 0].set_ylim(0, max(total_setup_times_per_period) * 1.2)\n", "\n", "    # 4. 各期間の総出荷遅れ量\n", "    axes[1, 1].bar(periods, total_shipment_delay_per_period, color='gold', alpha=0.7)\n", "    axes[1, 1].set_title('各期間の総出荷遅れ量')\n", "    axes[1, 1].set_xlabel('期間')\n", "    axes[1, 1].set_ylabel('総出荷遅れ量 (個)')\n", "    axes[1, 1].grid(True, alpha=0.3)\n", "    if total_shipment_delay_per_period:\n", "        axes[1, 1].set_ylim(0, max(total_shipment_delay_per_period) * 1.2)\n", "\n", "    plt.tight_layout()\n", "    \n", "    if save_path:\n", "        plt.savefig(save_path, dpi=300, bbox_inches='tight')\n", "        print(f\"プロットを保存: {save_path}\")\n", "    else:\n", "        plt.show()\n", "\n", "    time_violations = sum(1 for x in total_production_time_per_period if x > daily_regular_time + max_daily_work_time)\n", "    print(f\"時間制約違反: {time_violations} 期間\")\n", "    \n", "import os\n", "import time\n", "    \n", "def process_single_file(file_path):\n", "    \"\"\"単一のCSVファイルを処理する関数\"\"\"\n", "    global 品番数, 期間, 初期在庫量リスト\n", "    \n", "    print(f\"\\n=== Processing {file_path} ===\")\n", "    \n", "    # CSVファイルを読み込み\n", "    品番リスト, 出荷数リスト, 収容数リスト, サイクルタイムリスト, 込め数リスト, 初期在庫量リスト = read_csv(file_path)\n", "    品番数 = len(品番リスト)\n", "    期間 = 20\n", "    在庫コスト単価 = 180\n", "    出荷遅れコスト単価 = 500\n", "    \n", "    # 初期在庫量を調整\n", "    adjusted_initial_inventory = adjust_initial_inventory(初期在庫量リスト, 在庫コスト単価, 出荷遅れコスト単価, num_simulations=50, max_iterations=3)\n", "    初期在庫量リスト = adjusted_initial_inventory\n", "    \n", "    print(\"=== 混合整数計画法 スケジューリング ===\")\n", "    \n", "    # 計算時間を測定\n", "    start_time = time.time()\n", "    best_solution, best_cost = solve_mip(初期在庫量リスト)\n", "    calculation_time = time.time() - start_time\n", "    \n", "    if best_solution:\n", "        print(f\"\\n=== 最適化結果 ===\")\n", "        print(f\"最良個体の総コスト: {best_cost:.2f}\")\n", "        print(f\"計算時間: {calculation_time:.2f}秒\")\n", "        \n", "        # 結果をDataFrameに変換\n", "        result_df = pd.DataFrame(best_solution, \n", "                                index=[f\"品番_{i+1}\" for i in range(品番数)],\n", "                                columns=[f\"期間_{t+1}\" for t in range(期間)])\n", "        \n", "        # ファイル名から拡張子を除去\n", "        base_name = os.path.splitext(os.path.basename(file_path))[0]\n", "        \n", "        # 結果をCSVとして保存\n", "        result_csv_path = f\"result//MIP_results_{base_name}.csv\"\n", "        result_df.to_csv(result_csv_path, encoding='shift-jis')\n", "        print(f\"結果をCSVファイルに保存: {result_csv_path}\")\n", "        \n", "        # プロットを作成して保存\n", "        plot_path = f\"result//MIP_results_{base_name}.png\"\n", "        plot_results(best_solution, 初期在庫量リスト, save_path=plot_path)\n", "        \n", "        return best_cost, calculation_time, base_name\n", "    else:\n", "        print(\"\\n解が見つかりませんでした\")\n", "        return None, None, None\n", "\n", "def main():\n", "    \"\"\"メイン実行関数 - 全CSVファイルを処理\"\"\"\n", "    data_folder = \"data\"\n", "    \n", "    # dataフォルダ内のすべてのCSVファイルを取得\n", "    csv_files = [f for f in os.listdir(data_folder) if f.endswith('.csv')]\n", "    \n", "    if not csv_files:\n", "        print(\"dataフォルダにCSVファイルが見つかりません\")\n", "        return\n", "    \n", "    print(f\"見つかったCSVファイル: {csv_files}\")\n", "    \n", "    # 結果を格納するリスト\n", "    all_results = []\n", "    total_objective_value = 0\n", "    total_calculation_time = 0\n", "    \n", "    # 各CSVファイルを処理\n", "    for csv_file in csv_files:\n", "        file_path = os.path.join(data_folder, csv_file)\n", "        objective_value, calc_time, file_name = process_single_file(file_path)\n", "        \n", "        if objective_value is not None:\n", "            all_results.append({\n", "                'ファイル名': file_name,\n", "                '目的関数値': objective_value,\n", "                '計算時間': calc_time\n", "            })\n", "            total_objective_value += objective_value\n", "            total_calculation_time += calc_time\n", "    \n", "    # 集計結果をDataFrameに変換\n", "    summary_df = pd.DataFrame(all_results)\n", "    \n", "    # 合計行を追加\n", "    summary_row = pd.DataFrame({\n", "        'ファイル名': ['合計'],\n", "        '目的関数値': [total_objective_value],\n", "        '計算時間': [total_calculation_time]\n", "    })\n", "    summary_df = pd.concat([summary_df, summary_row], ignore_index=True)\n", "    \n", "    # 集計結果をCSVとして保存\n", "    summary_csv_path = \"result//MIP_aggregate_results.csv\"\n", "    summary_df.to_csv(summary_csv_path, encoding='shift-jis', index=False)\n", "    print(f\"\\n集計結果をCSVファイルに保存: {summary_csv_path}\")\n", "    \n", "    # 集計結果のプロットを作成\n", "    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 6))\n", "    \n", "    # 目的関数値のプロット（合計を除く）\n", "    individual_results = summary_df[summary_df['ファイル名'] != '合計']\n", "    ax1.bar(individual_results['ファイル名'], individual_results['目的関数値'], \n", "            color='skyblue', alpha=0.7)\n", "    ax1.set_title('各データセットの目的関数値')\n", "    ax1.set_xlabel('データセット')\n", "    ax1.set_ylabel('目的関数値')\n", "    ax1.tick_params(axis='x', rotation=45)\n", "    ax1.grid(True, alpha=0.3)\n", "    \n", "    # 計算時間のプロット（合計を除く）\n", "    ax2.bar(individual_results['ファイル名'], individual_results['計算時間'], \n", "            color='lightgreen', alpha=0.7)\n", "    ax2.set_title('各データセットの計算時間')\n", "    ax2.set_xlabel('データセット')\n", "    ax2.set_ylabel('計算時間 (秒)')\n", "    ax2.tick_params(axis='x', rotation=45)\n", "    ax2.grid(True, alpha=0.3)\n", "    \n", "    plt.tight_layout()\n", "    \n", "    # 集計プロットを保存\n", "    #aggregate_plot_path = \"aggregate_results.png\"\n", "    #plt.savefig(aggregate_plot_path, dpi=300, bbox_inches='tight')\n", "    #plt.show()\n", "    #print(f\"集計プロットを画像ファイルに保存: {aggregate_plot_path}\")\n", "    \n", "    # 結果の要約を表示\n", "    print(f\"\\n=== 全体の集計結果 ===\")\n", "    print(f\"処理したファイル数: {len(all_results)}\")\n", "    print(f\"総目的関数値: {total_objective_value:.2f}\")\n", "    print(f\"総計算時間: {total_calculation_time:.2f}秒\")\n", "    print(f\"平均目的関数値: {total_objective_value/len(all_results):.2f}\")\n", "    print(f\"平均計算時間: {total_calculation_time/len(all_results):.2f}秒\")\n", "    \n", "if __name__ == \"__main__\":\n", "    main()"]}, {"cell_type": "markdown", "id": "c943cbb4", "metadata": {}, "source": ["厳密解を得られない時にどうやって終わるか  \n", "500秒で終わり？  \n", "何%の解なら得られているのか？  "]}, {"cell_type": "markdown", "id": "2ee490f6", "metadata": {}, "source": []}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.7"}}, "nbformat": 4, "nbformat_minor": 5}