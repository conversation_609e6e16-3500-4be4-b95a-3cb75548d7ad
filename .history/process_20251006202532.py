import pandas as pd
import csv
import random
import numpy as np
import matplotlib.pyplot as plt
import japanize_matplotlib
import os
import glob
import time

# グローバル変数
品番リスト = []
出荷数リスト = []
収容数リスト = []
サイクルタイムリスト = []
込め数リスト = []
初期在庫量リスト = []

# コストとペナルティの係数
在庫コスト単価 = 180
残業コスト単価 = 66.7
段替えコスト単価 = 400
出荷遅れコスト単価 = 500

定時 = 8 * 60 * 2
最大残業時間 = 2 * 60 * 2
段替え時間 = 30

def read_csv(file_path):
    """CSVファイルを読み込む関数"""
    global 品番リスト, 出荷数リスト, 収容数リスト, サイクルタイムリスト, 込め数リスト, 初期在庫量リスト
    
    収容数辞書 = {}
    with open('収容数.csv', 'r', encoding='shift-jis') as capacity_file:
        capacity_reader = csv.reader(capacity_file)
        capacity_header = next(capacity_reader)
        for row in capacity_reader:
            if len(row) >= 2 and row[1].strip():  # Skip if second column is empty
                品番 = row[0]  # 品番列
                収容数 = int(float(row[1]))  # 収容数列
                収容数辞書[品番] = 収容数

    with open(file_path, 'r', encoding='shift-jis') as file:
        reader = csv.reader(file)
        header = next(reader)
        
        品番リスト = []
        出荷数リスト = []
        収容数リスト = []
        サイクルタイムリスト = []
        込め数リスト = []
        
        # 期間数（日数）を定義
        期間数 = 20
        
        rows = list(reader)
        for row in rows:
            if len(row) == 0:
                continue
            
            # 個数を取得
            total_quantity = int(row[header.index("個数")])
            
            # 個数が200未満の場合はスキップ
            if total_quantity < 200:
                continue
            
            # 1日あたりの出荷数を計算（総期間で割る）
            daily_quantity = total_quantity / 期間数
            
            品番リスト.append(row[header.index("素材品番")])
            出荷数リスト.append(daily_quantity)
            込め数リスト.append(int(float(row[header.index("込数")])))
            
            cycle_time_per_unit = float(row[header.index("サイクルタイム")]) / 60
            サイクルタイムリスト.append(cycle_time_per_unit)
            
            収容数リスト.append(収容数辞書.get(品番, 80))
            
    
        # 出荷数に基づいて初期在庫量（処理前）をランダムに設定
        初期在庫量リスト = []
        for shipment in 出荷数リスト:
            random_inventory = random.randint(int(shipment * 3), int(shipment * 5))
            初期在庫量リスト.append(random_inventory)
            
    return 品番リスト, 出荷数リスト, 収容数リスト, サイクルタイムリスト, 込め数リスト, 初期在庫量リスト

def simulate_production_schedule(initial_inventory, 期間=20):
    """生産スケジュールをシミュレートする関数（統一版）"""
    品番数 = len(initial_inventory)
    inventory = initial_inventory[:]
    inventory_history = [[] for _ in range(品番数)]
    
    daily_regular_time = 8 * 60 * 2  # 1日の通常稼働時間（分）
    max_daily_overtime = 2 * 60 * 2  # 1日の最大残業時間（分）
    max_daily_work_time = daily_regular_time + max_daily_overtime
    
    for t in range(期間):
        daily_production_time = 0
        daily_setup_count = 0
        
        # 各品番の需要を処理
        for i in range(品番数):
            demand = 出荷数リスト[i]
            inventory[i] -= demand
            
            # 在庫が不足する場合は生産
            if inventory[i] < 0:
                shortage = abs(inventory[i])
                # 生産量を決定（不足分を補う）
                production = shortage
                
                # 生産時間を計算
                if production > 0:
                    daily_setup_count += 1
                    setup_time = 30  # 段替え時間
                    production_time = (production / 込め数リスト[i]) * サイクルタイムリスト[i]
                    daily_production_time += production_time + setup_time
                
                inventory[i] += production
            
            # 在庫履歴に記録
            inventory_history[i].append(max(0, inventory[i]))
        
        # 稼働時間制約チェック（制約違反の場合は一部生産を削減）
        if daily_production_time > max_daily_work_time:
            # 簡易的な調整：超過分を比例配分で削減
            reduction_factor = max_daily_work_time / daily_production_time
            for i in range(品番数):
                if inventory[i] > 0:
                    # 生産量を削減し、在庫を調整
                    current_production = max(0, 出荷数リスト[i] - (initial_inventory[i] - inventory[i]))
                    reduced_production = current_production * reduction_factor
                    inventory[i] = initial_inventory[i] + reduced_production - 出荷数リスト[i] * (t + 1)
                    inventory[i] = max(0, inventory[i])
                    inventory_history[i][-1] = inventory[i]
    
    return inventory_history

def adjust_initial_inventory(初期在庫量リスト, 在庫コスト単価=180, 出荷遅れコスト単価=500, num_simulations=50, max_iterations=3):
    """初期在庫を調整する関数（統一版）"""
    
    品番数 = len(初期在庫量リスト)
    s = 初期在庫量リスト[:]
    
    # h / (h+c) - 最適在庫理論に基づく計算
    prob_target = 在庫コスト単価 / (在庫コスト単価 + 出荷遅れコスト単価)
    
    print(f"\n=== 初期在庫水準の調整アルゴリズム開始 ===")
    
    for iteration in range(max_iterations):
        print(f"--- 調整イテレーション {iteration + 1} ---")
        
        # 各在庫点について在庫量の分布を求める
        inventory_distributions = [[] for _ in range(品番数)]
        for _ in range(num_simulations):
            inventory_history = simulate_production_schedule(s)
            for i in range(品番数):
                inventory_distributions[i].extend(inventory_history[i])
        
        adjustments = [0] * 品番数
        
        # 各在庫点について在庫量の最適調整量r^*を求める
        for i in range(品番数):
            if not inventory_distributions[i]:
                continue
            
            inventory_counts = pd.Series(inventory_distributions[i]).value_counts(normalize=True).sort_index()
            cumulative_distribution = inventory_counts.cumsum()
            
            # sum_{x <= r-1} f(x) <= h / (h + c) の条件を満たす最小のrを見つける
            best_r = 0
            for r in cumulative_distribution.index:
                prob_at_r_minus_1 = cumulative_distribution.get(r - 1, 0)
                if prob_at_r_minus_1 <= prob_target:
                    best_r = r
                else:
                    break
            
            # 調整量を計算
            current_avg = np.mean(inventory_distributions[i]) if inventory_distributions[i] else 0
            adjustments[i] = max(0, best_r - current_avg)
        
        print(f"  今回の調整量: {adjustments}")
        
        # 在庫量を更新
        for i in range(品番数):
            s[i] = max(0, s[i] - adjustments[i])
        
        print(f"  更新後の初期在庫量: {s}")
    
    print("--- 最大反復回数に到達しました。---")
    return s

def calculate_total_cost(production_schedule, initial_inventory, 期間=20):
    """総コストを計算する関数"""
    品番数 = len(initial_inventory)
    
    # 在庫コスト
    inventory_cost = 0
    inventory = initial_inventory[:]
    
    # セットアップコスト
    setup_cost = 0
    
    # 残業コスト
    overtime_cost = 0
    
    # 出荷遅れコスト
    shortage_cost = 0
    
    for t in range(期間):
        daily_work_time = 0
        
        for i in range(品番数):
            # 生産
            production = production_schedule[i][t] if production_schedule else 0
            
            # セットアップコスト
            if production > 0:
                setup_cost += 段替えコスト単価
                daily_work_time += 段替え時間
                daily_work_time += (production / 込め数リスト[i]) * サイクルタイムリスト[i]
            
            # 在庫更新
            inventory[i] += production
            inventory[i] -= 出荷数リスト[i]
            
            # 在庫コスト
            if inventory[i] > 0:
                inventory_cost += (inventory[i] / 収容数リスト[i]) * 在庫コスト単価
            
            # 出荷遅れコスト
            if inventory[i] < 0:
                shortage_cost += abs(inventory[i]) * 出荷遅れコスト単価
                inventory[i] = 0
        
        # 残業コスト
        if daily_work_time > 定時:
            overtime = min(daily_work_time - 定時, 最大残業時間)
            overtime_cost += overtime * 残業コスト単価
    
    total_cost = inventory_cost + setup_cost + overtime_cost + shortage_cost
    return total_cost

def plot_result(production_schedule, initial_inventory, file_name, method_name="", 期間=20):
    """結果をプロットする関数（共通版）"""
    import matplotlib.pyplot as plt
    import japanize_matplotlib
    import os

    品番数 = len(品番リスト)

    # 在庫推移を計算
    inventory_levels = []
    current_inventory = initial_inventory[:]

    for t in range(期間):
        period_inventory = []
        for i in range(品番数):
            current_inventory[i] += production_schedule[i][t] if production_schedule else 0
            current_inventory[i] -= 出荷数リスト[i]
            current_inventory[i] = max(0, current_inventory[i])
            period_inventory.append(current_inventory[i])
        inventory_levels.append(period_inventory)

    # プロット作成
    fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(15, 10))

    # 生産量のプロット
    if production_schedule:
        for i in range(min(5, 品番数)):  # 最初の5品番のみ表示
            ax1.plot(range(期間), [production_schedule[i][t] for t in range(期間)],
                    marker='o', label=f'品番{i+1}: {品番リスト[i]}')

    ax1.set_title(f'生産量推移 ({method_name})')
    ax1.set_xlabel('期間')
    ax1.set_ylabel('生産量')
    ax1.legend()
    ax1.grid(True)

    # 在庫量のプロット
    for i in range(min(5, 品番数)):  # 最初の5品番のみ表示
        ax2.plot(range(期間), [inventory_levels[t][i] for t in range(期間)],
                marker='s', label=f'品番{i+1}: {品番リスト[i]}')

    ax2.set_title(f'在庫量推移 ({method_name})')
    ax2.set_xlabel('期間')
    ax2.set_ylabel('在庫量')
    ax2.legend()
    ax2.grid(True)

    plt.tight_layout()

    # 結果フォルダが存在しない場合は作成
    os.makedirs('result', exist_ok=True)

    # プロットを保存
    plot_filename = f'result/{method_name}_results_{file_name}.png' if method_name else f'result/results_{file_name}.png'
    plt.savefig(plot_filename)
    print(f"プロットを保存: {plot_filename}")

    plt.show()

    # 時間制約違反をチェック
    violation_count = 0
    if production_schedule:
        for t in range(期間):
            daily_time = 0
            for i in range(品番数):
                if production_schedule[i][t] > 0:
                    daily_time += 段替え時間
                    daily_time += (production_schedule[i][t] / 込め数リスト[i]) * サイクルタイムリスト[i]

            if daily_time > 定時 + 最大残業時間:
                violation_count += 1

    print(f"時間制約違反: {violation_count} 期間")
    return violation_count

def get_data_info():
    """データ情報を取得する関数"""
    return {
        '品番リスト': 品番リスト,
        '出荷数リスト': 出荷数リスト,
        '収容数リスト': 収容数リスト,
        'サイクルタイムリスト': サイクルタイムリスト,
        '込め数リスト': 込め数リスト,
        '初期在庫量リスト': 初期在庫量リスト,
        '品番数': len(品番リスト),
        '期間': 20
    }
