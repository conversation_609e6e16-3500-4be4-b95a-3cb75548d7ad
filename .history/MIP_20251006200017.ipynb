{"cells": [{"cell_type": "markdown", "id": "1", "metadata": {}, "source": ["# 混合整数計画法による生産スケジューリング\n", "\n", "process.pyの共通関数を使用してMIPによる最適化を実行します。"]}, {"cell_type": "code", "execution_count": null, "id": "2", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import pulp\n", "import matplotlib.pyplot as plt\n", "import japanize_matplotlib\n", "import os\n", "import glob\n", "import time\n", "from process import (\n", "    read_csv, adjust_initial_inventory, calculate_total_cost, get_data_info, plot_result,\n", "    在庫コスト単価, 残業コスト単価, 段替えコスト単価, 出荷遅れコスト単価,\n", "    定時, 最大残業時間, 段替え時間\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "3", "metadata": {}, "outputs": [], "source": ["def solve_mip(initial_inventory_list_arg, 品番数, 期間=20):\n", "    \"\"\"PuLPを用いてMIPを解く関数\"\"\"\n", "    \n", "    # データ情報を取得\n", "    data_info = get_data_info()\n", "    出荷数リスト = data_info['出荷数リスト']\n", "    収容数リスト = data_info['収容数リスト']\n", "    サイクルタイムリスト = data_info['サイクルタイムリスト']\n", "    込め数リスト = data_info['込め数リスト']\n", "    \n", "    # モデルの定義\n", "    model = pulp.LpProblem(\"ProductionScheduling\", pulp.LpMinimize)\n", "    \n", "    # インデックスの定義\n", "    品目 = range(品番数)\n", "    期間_index = range(期間)\n", "\n", "    # 決定変数\n", "    Production = pulp.LpVariable.dicts(\"Production\", (品目, 期間_index), lowBound=0, cat='Integer')\n", "    IsProduced = pulp.LpVariable.dicts(\"IsProduced\", (品目, 期間_index), cat='Binary')\n", "    Inventory = pulp.LpVariable.dicts(\"Inventory\", (品目, 期間_index), lowBound=0, cat='Continuous')\n", "    Shortage = pulp.LpVariable.dicts(\"Shortage\", (品目, 期間_index), lowBound=0, cat='Continuous')\n", "    WorkTime = pulp.LpVariable.dicts(\"WorkTime\", 期間_index, lowBound=0, cat='Continuous')\n", "    Overtime = pulp.LpVariable.dicts(\"Overtime\", 期間_index, lowBound=0, cat='Continuous')\n", "\n", "    # 目的関数\n", "    total_cost = pulp.lpSum(\n", "        在庫コスト単価 * Inventory[i][t]/収容数リスト[i] for i in 品目 for t in 期間_index\n", "    ) + pulp.lpSum(\n", "        残業コスト単価 * Overtime[t] for t in 期間_index\n", "    ) + pulp.lpSum(\n", "        段替えコスト単価 * IsProduced[i][t] for i in 品目 for t in 期間_index\n", "    ) + pulp.lpSum(\n", "        出荷遅れコスト単価 * Shortage[i][t] for i in 品目 for t in 期間_index\n", "    )\n", "    \n", "    model += total_cost, \"Total Cost\"\n", "\n", "    # 制約条件\n", "    bigM = 1000000\n", "\n", "    for i in 品目:\n", "        for t in 期間_index:\n", "            if t == 0:\n", "                # 初期在庫リストを使用\n", "                model += Inventory[i][t] - Shortage[i][t] == initial_inventory_list_arg[i] + Production[i][t] - 出荷数リスト[i]\n", "            else:\n", "                model += Inventory[i][t] - Shortage[i][t] == Inventory[i][t-1] - Shortage[i][t-1] + Production[i][t] - 出荷数リスト[i]\n", "            \n", "            model += Production[i][t] <= bigM * IsProduced[i][t]\n", "\n", "    for t in 期間_index:\n", "        model += WorkTime[t] == pulp.lpSum(\n", "            Production[i][t] * (サイクルタイムリスト[i] / 込め数リスト[i]) + 段替え時間 * IsProduced[i][t]\n", "            for i in 品目\n", "        )\n", "        \n", "        model += WorkTime[t] <= 定時 + Overtime[t]\n", "        model += WorkTime[t] <= 定時 + 最大残業時間\n", "        model += Overtime[t] >= WorkTime[t] - 定時\n", "        model += Overtime[t] >= 0\n", "\n", "    # Solverの設定\n", "    solver = pulp.GUROBI(msg=True, timelimit=500)\n", "    \n", "    # 最適化の実行\n", "    model.solve(solver)\n", "    \n", "    # ソルバーの詳細情報を取得\n", "    status = pulp.LpStatus[model.status]\n", "    print(\"ステータス:\", status)\n", "    \n", "    # 精度情報を格納する辞書\n", "    accuracy_info = {}\n", "    \n", "    # Gurobiソルバーの詳細情報を取得\n", "    if hasattr(solver, 'solverModel') and solver.solverModel is not None:\n", "        gurobi_model = solver.solverModel\n", "        \n", "        # 最適性ギャップの取得\n", "        if hasattr(gurobi_model, 'MIPGap'):\n", "            gap = gurobi_model.MIPGap * 100  # パーセンテージに変換\n", "            accuracy_info['gap'] = gap\n", "        \n", "        # 目的関数値と境界値の取得\n", "        if hasattr(gurobi_model, 'ObjVal'):\n", "            obj_val = gurobi_model.ObjVal\n", "            accuracy_info['obj_val'] = obj_val\n", "            \n", "        if hasattr(gurobi_model, 'ObjBound'):\n", "            obj_bound = gurobi_model.ObjBound\n", "            accuracy_info['obj_bound'] = obj_bound\n", "            \n", "        # ソルバーステータスの詳細\n", "        if hasattr(gurobi_model, 'Status'):\n", "            gurobi_status = gurobi_model.Status\n", "            accuracy_info['gurobi_status'] = gurobi_status\n", "            print(f\"Gurobi status= {gurobi_status}\")\n", "    \n", "    if status == 'Optimal':\n", "        print(\"総コスト:\", pulp.value(model.objective))\n", "\n", "        production_schedule = [[0] * 期間 for _ in range(品番数)]\n", "        for i in 品目:\n", "            for t in 期間_index:\n", "                production_schedule[i][t] = pulp.value(Production[i][t])\n", "\n", "        return production_schedule, pulp.value(model.objective), accuracy_info\n", "    elif status == 'Not Solved':\n", "        # 時間制限などで最適解が見つからなかった場合でも、実行可能解があれば取得\n", "        if pulp.value(model.objective) is not None:\n", "            print(\"時間制限により最適解は見つかりませんでしたが、実行可能解を取得しました\")\n", "            print(\"総コスト:\", pulp.value(model.objective))\n", "            \n", "            production_schedule = [[0] * 期間 for _ in range(品番数)]\n", "            for i in 品目:\n", "                for t in 期間_index:\n", "                    production_schedule[i][t] = pulp.value(Production[i][t])\n", "\n", "            return production_schedule, pulp.value(model.objective), accuracy_info\n", "    \n", "    return None, None, accuracy_info"]}, {"cell_type": "code", "execution_count": null, "id": "5", "metadata": {}, "outputs": [], "source": ["def save_results_to_csv(production_schedule, initial_inventory, total_cost, file_name, calculation_time, accuracy_info):\n", "    \"\"\"結果をCSVファイルに保存する関数\"\"\"\n", "    data_info = get_data_info()\n", "    品番リスト = data_info['品番リスト']\n", "    品番数 = len(品番リスト)\n", "    期間 = 20\n", "    \n", "    # 結果フォルダが存在しない場合は作成\n", "    os.makedirs('result', exist_ok=True)\n", "    \n", "    # 結果をDataFrameに変換\n", "    results_data = []\n", "    \n", "    # ヘッダー情報\n", "    results_data.append(['ファイル名', file_name])\n", "    results_data.append(['総コスト', total_cost])\n", "    results_data.append(['計算時間(秒)', calculation_time])\n", "    \n", "    # 精度情報を追加\n", "    if 'gap' in accuracy_info:\n", "        results_data.append(['最適性ギャップ(%)', accuracy_info['gap']])\n", "    if 'gurobi_status' in accuracy_info:\n", "        results_data.append(['Gurobiステータス', accuracy_info['gurobi_status']])\n", "    \n", "    results_data.append([''])  # 空行\n", "    \n", "    # 生産スケジュール\n", "    if production_schedule:\n", "        header = ['品番'] + [f'期間{t+1}' for t in range(期間)]\n", "        results_data.append(header)\n", "        \n", "        for i in range(品番数):\n", "            row = [品番リスト[i]] + [production_schedule[i][t] for t in range(期間)]\n", "            results_data.append(row)\n", "    \n", "    # CSVファイルに保存\n", "    csv_filename = f'result/MIP_results_{file_name}.csv'\n", "    with open(csv_filename, 'w', newline='', encoding='utf-8') as csvfile:\n", "        writer = csv.writer(csvfile)\n", "        writer.writerows(results_data)\n", "    \n", "    print(f\"結果をCSVファイルに保存: {csv_filename}\")"]}, {"cell_type": "code", "execution_count": null, "id": "6", "metadata": {}, "outputs": [], "source": ["# メイン実行部分\n", "def main():\n", "    \"\"\"メイン実行関数\"\"\"\n", "    # dataフォルダ内のCSVファイルを取得\n", "    csv_files = glob.glob('data/*.csv')\n", "    print(f\"見つかったCSVファイル: {[os.path.basename(f) for f in csv_files]}\")\n", "    \n", "    # 集計用のリスト\n", "    all_results = []\n", "    \n", "    for csv_file in csv_files:\n", "        file_name = os.path.splitext(os.path.basename(csv_file))[0]\n", "        print(f\"\\n=== Processing {csv_file} ===\")\n", "        \n", "        # CSVファイルを読み込み\n", "        read_csv(csv_file)\n", "        data_info = get_data_info()\n", "        品番数 = data_info['品番数']\n", "        初期在庫量リスト = data_info['初期在庫量リスト']\n", "        \n", "        # 初期在庫量を調整\n", "        adjusted_inventory = adjust_initial_inventory(初期在庫量リスト)\n", "        \n", "        # MIPによる最適化\n", "        print(\"=== 混合整数計画法 スケジューリング ===\")\n", "        start_time = time.time()\n", "        \n", "        production_schedule, total_cost, accuracy_info = solve_mip(adjusted_inventory, 品番数)\n", "        \n", "        calculation_time = time.time() - start_time\n", "        \n", "        if total_cost is not None:\n", "            print(f\"\\n=== 最適化結果 ===\")\n", "            print(f\"最良個体の総コスト: {total_cost:.2f}\")\n", "            print(f\"計算時間: {calculation_time:.2f}秒\")\n", "            \n", "            # 結果をCSVファイルに保存\n", "            save_results_to_csv(production_schedule, adjusted_inventory, total_cost, \n", "                               file_name, calculation_time, accuracy_info)\n", "            \n", "            # 結果をプロット\n", "            print(f\"\\n=== 結果のプロット ===\")\n", "            violation_count = plot_result(production_schedule, adjusted_inventory, file_name)\n", "            \n", "            # 集計用データに追加\n", "            all_results.append({\n", "                'file_name': file_name,\n", "                'total_cost': total_cost,\n", "                'calculation_time': calculation_time,\n", "                'violation_count': violation_count,\n", "                'accuracy_info': accuracy_info\n", "            })\n", "        else:\n", "            print(\"最適化に失敗しました\")\n", "    \n", "    # 集計結果を保存\n", "    if all_results:\n", "        save_aggregate_results(all_results)\n", "        plot_aggregate_results(all_results)\n", "\n", "def save_aggregate_results(all_results):\n", "    \"\"\"集計結果をCSVファイルに保存\"\"\"\n", "    os.makedirs('result', exist_ok=True)\n", "    \n", "    # 集計データの作成\n", "    summary_data = []\n", "    summary_data.append(['ファイル名', '総コスト', '計算時間(秒)', '時間制約違反期間数', '最適性ギャップ(%)', 'Gurobiステータス'])\n", "    \n", "    total_cost_sum = 0\n", "    total_time_sum = 0\n", "    \n", "    for result in all_results:\n", "        gap = result['accuracy_info'].get('gap', 'N/A')\n", "        status = result['accuracy_info'].get('gurobi_status', 'N/A')\n", "        \n", "        summary_data.append([\n", "            result['file_name'],\n", "            f\"{result['total_cost']:.2f}\",\n", "            f\"{result['calculation_time']:.2f}\",\n", "            result['violation_count'],\n", "            f\"{gap:.4f}\" if isinstance(gap, (int, float)) else gap,\n", "            status\n", "        ])\n", "        \n", "        total_cost_sum += result['total_cost']\n", "        total_time_sum += result['calculation_time']\n", "    \n", "    # 合計行を追加\n", "    summary_data.append(['合計', f\"{total_cost_sum:.2f}\", f\"{total_time_sum:.2f}\", '', '', ''])\n", "    \n", "    # CSVファイルに保存\n", "    with open('result/MIP_aggregate_results.csv', 'w', newline='', encoding='utf-8') as csvfile:\n", "        writer = csv.writer(csvfile)\n", "        writer.writerows(summary_data)\n", "    \n", "    print(f\"\\n=== 集計結果 ===\")\n", "    print(f\"総コストの合計: {total_cost_sum:.2f}\")\n", "    print(f\"計算時間の合計: {total_time_sum:.2f}秒\")\n", "    print(\"集計結果をCSVファイルに保存: result/MIP_aggregate_results.csv\")\n", "\n", "def plot_aggregate_results(all_results):\n", "    \"\"\"集計結果をプロット\"\"\"\n", "    file_names = [result['file_name'] for result in all_results]\n", "    total_costs = [result['total_cost'] for result in all_results]\n", "    calculation_times = [result['calculation_time'] for result in all_results]\n", "    \n", "    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 6))\n", "    \n", "    # 総コストのプロット\n", "    ax1.bar(file_names, total_costs, color='skyblue')\n", "    ax1.set_title('各データセットの総コスト (MIP)')\n", "    ax1.set_xlabel('データセット')\n", "    ax1.set_ylabel('総コスト')\n", "    ax1.tick_params(axis='x', rotation=45)\n", "    \n", "    # 計算時間のプロット\n", "    ax2.bar(file_names, calculation_times, color='lightcoral')\n", "    ax2.set_title('各データセットの計算時間 (MIP)')\n", "    ax2.set_xlabel('データセット')\n", "    ax2.set_ylabel('計算時間 (秒)')\n", "    ax2.tick_params(axis='x', rotation=45)\n", "    \n", "    plt.tight_layout()\n", "    plt.savefig('result/MIP_aggregate_plot.png')\n", "    print(\"集計プロットを保存: result/MIP_aggregate_plot.png\")\n", "    plt.show()\n", "\n", "# 実行\n", "if __name__ == \"__main__\":\n", "    import csv\n", "    main()"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.5"}}, "nbformat": 4, "nbformat_minor": 5}