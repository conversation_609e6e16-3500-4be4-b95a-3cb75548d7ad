{"cells": [{"cell_type": "code", "execution_count": 2, "id": "6e757c78", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["見つかったCSVファイル: ['D36.csv']\n", "\n", "=== Processing data/D36.csv ===\n", "\n", "=== 初期在庫水準の調整アルゴリズム開始 ===\n", "--- 調整イテレーション 1 ---\n", "  今回の調整量: [163.63636363636363, 351.81818181818176, 30.909090909090907, 42.54545454545455, 500.909090909091, 1130.181818181818]\n", "  更新後の初期在庫量: [393.3636363636364, 1399.1818181818182, 82.0909090909091, 113.45454545454545, 1311.090909090909, 3051.818181818182]\n", "--- 調整イテレーション 2 ---\n", "  今回の調整量: [163.63636363636363, 351.81818181818176, 30.909090909090907, 42.54545454545455, 500.909090909091, 1130.1818181818182]\n", "  更新後の初期在庫量: [229.72727272727275, 1047.3636363636365, 51.18181818181819, 70.9090909090909, 810.181818181818, 1921.6363636363637]\n", "--- 調整イテレーション 3 ---\n", "  今回の調整量: [163.63636363636363, 351.81818181818176, 30.90909090909091, 42.54545454545455, 500.90909090909093, 1130.1818181818182]\n", "  更新後の初期在庫量: [66.09090909090912, 695.5454545454547, 20.272727272727277, 28.36363636363636, 309.2727272727271, 791.4545454545455]\n", "--- 最大反復回数に到達しました。---\n", "=== 多スタート焼きなまし法 スケジューリング ===\n", "--- Start 1/5 ---\n", "  New best solution found with total cost: 8773.39\n", "--- Start 2/5 ---\n", "--- Start 3/5 ---\n", "--- Start 4/5 ---\n", "--- Start 5/5 ---\n", "\n", "=== 最適化結果 ===\n", "最良個体の総コスト: 8773.39\n", "計算時間: 4.80秒\n"]}, {"data": {"image/png": "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*************************************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", "text/plain": ["<Figure size 1200x800 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", "=== 結果のプロット ===\n", "プロットを保存: result/sa_results_D36.png\n", "\n", "集計結果をCSVファイルに保存: result/simulated_annealing_aggregate_results.csv\n", "\n", "=== 全体の集計結果 ===\n", "処理したファイル数: 1\n", "総目的関数値: 8773.39\n", "総計算時間: 4.80秒\n", "平均目的関数値: 8773.39\n", "平均計算時間: 4.80秒\n"]}, {"data": {"image/png": "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", "text/plain": ["<Figure size 1600x1200 with 4 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["import random\n", "import numpy as np\n", "import pandas as pd\n", "import csv\n", "import matplotlib.pyplot as plt\n", "import japanize_matplotlib\n", "import copy\n", "import os\n", "import time\n", "\n", "# グローバル変数\n", "品番リスト = []\n", "出荷数リスト = []\n", "収容数リスト = []\n", "サイクルタイムリスト = []\n", "込め数リスト = []\n", "初期在庫量リスト = []\n", "品番数 = 0\n", "期間 = 0\n", "\n", "# コストとペナルティの係数\n", "在庫コスト単価 = 180\n", "残業コスト単価 = 66.7\n", "段替えコスト単価 = 400\n", "出荷遅れコスト単価 = 500\n", "\n", "定時 = 8 * 60 * 2\n", "最大残業時間 = 2 * 60 * 2\n", "段替え時間 = 30\n", "\n", "def read_csv(file_path):\n", "    \"\"\"CSVファイルを読み込む関数\"\"\"\n", "    global 品番リスト, 出荷数リスト, 容数リスト, サイクルタイムリスト, 込め数リスト, 初期在庫量リスト\n", "    \n", "    収容数辞書 = {}\n", "    try:\n", "        with open('収容数.csv', 'r', encoding='shift-jis') as capacity_file:\n", "            capacity_reader = csv.reader(capacity_file)\n", "            next(capacity_reader) # ヘッダーをスキップ\n", "            for row in capacity_reader:\n", "                if len(row) >= 2 and row[1].strip():\n", "                    品番 = row[0]\n", "                    収容数 = int(float(row[1]))\n", "                    収容数辞書[品番] = 収容数\n", "    except FileNotFoundError:\n", "        print(\"収容数.csvが見つかりません。デフォルト値（80）を使用します。\")\n", "    \n", "    with open(file_path, 'r', encoding='shift-jis') as file:\n", "        reader = csv.reader(file)\n", "        header = next(reader)\n", "        \n", "        品番リスト.clear()\n", "        出荷数リスト.clear()\n", "        収容数リスト.clear()\n", "        サイクルタイムリスト.clear()\n", "        込め数リスト.clear()\n", "        \n", "        期間数 = 22 # 期間数（日数）を定義\n", "        \n", "        rows = list(reader)\n", "        for row in rows:\n", "            if len(row) == 0:\n", "                continue\n", "            \n", "            total_quantity = int(row[header.index(\"個数\")])\n", "            \n", "            if total_quantity < 200:\n", "                continue\n", "            \n", "            daily_quantity = total_quantity / 期間数\n", "            \n", "            品番 = row[header.index(\"素材品番\")]\n", "            品番リスト.append(品番)\n", "            出荷数リスト.append(daily_quantity)\n", "            込め数リスト.append(int(float(row[header.index(\"込数\")])))\n", "            \n", "            cycle_time_per_unit = float(row[header.index(\"サイクルタイム\")]) / 60\n", "            サイクルタイムリスト.append(cycle_time_per_unit)\n", "            \n", "            収容数リスト.append(収容数辞書.get(品番, 80))\n", "            \n", "        初期在庫量リスト.clear()\n", "        for shipment in 出荷数リスト:\n", "            random_inventory = random.randint(int(shipment * 3), int(shipment * 5))\n", "            初期在庫量リスト.append(random_inventory)\n", "            \n", "    return 品番リスト, 出荷数リスト, 収容数リスト, サイクルタイムリスト, 込め数リスト, 初期在庫量リスト\n", "\n", "def evaluate(solution, current_initial_inventory):\n", "    \"\"\"総コスト（在庫、残業、段替え、出荷遅れ）を最小化する評価関数\"\"\"\n", "    \n", "    total_inventory_cost = 0\n", "    total_overtime_cost = 0\n", "    total_setup_cost = 0\n", "    total_shipment_delay_cost = 0\n", "    \n", "    inventory = current_initial_inventory[:]\n", "    inventory_history = [[] for _ in range(品番数)]\n", "    \n", "    for t in range(期間):\n", "        daily_time = 0\n", "        daily_setup_count = 0\n", "        \n", "        for i in range(品番数):\n", "            production = solution[t][i]\n", "            \n", "            if production > 0:\n", "                # 既にその日に生産している品番があるかチェックし、段替えコストを計算\n", "                is_first_production_today = True\n", "                for j in range(i):\n", "                    if solution[t][j] > 0:\n", "                        is_first_production_today = False\n", "                        break\n", "                if is_first_production_today:\n", "                    daily_setup_count += 1\n", "            \n", "            setup_time = 段替え時間 if production > 0 else 0\n", "            \n", "            production_time = (production / 込め数リスト[i]) * サイクルタイムリスト[i]\n", "            daily_time += production_time + setup_time\n", "            \n", "            inventory[i] += production - 出荷数リスト[i]\n", "            inventory_history[i].append(inventory[i])\n", "            \n", "            if inventory[i] < 0:\n", "                shortage_amount = abs(inventory[i])\n", "                total_shipment_delay_cost += 出荷遅れコスト単価 * shortage_amount\n", "            \n", "            if inventory[i] > 0:\n", "                total_inventory_cost += (inventory[i] / 収容数リスト[i]) * 在庫コスト単価\n", "        \n", "        total_setup_cost += 段替えコスト単価 * daily_setup_count\n", "        \n", "        if daily_time > 定時:\n", "            overtime = daily_time - 定時\n", "            total_overtime_cost += 残業コスト単価 * overtime\n", "        \n", "        if daily_time > 定時 + 最大残業時間:\n", "            work_time_penalty = (daily_time - (定時 + 最大残業時間)) * (残業コスト単価 * 1000)\n", "            total_overtime_cost += work_time_penalty\n", "            \n", "    total_cost = total_inventory_cost + total_overtime_cost + total_setup_cost + total_shipment_delay_cost\n", "    \n", "    return total_cost, inventory_history\n", "\n", "def generate_initial_solution(current_initial_inventory):\n", "    \"\"\"\n", "    需要予測と生産量平準化を考慮した初期解を生成する関数\n", "    \"\"\"\n", "    solution = []\n", "    temp_inventory = current_initial_inventory[:]\n", "    max_daily_work_time = (8 + 2) * 60 * 2\n", "    look_ahead_period = 3  # 3日先の需要まで見る\n", "\n", "    for t in range(期間):\n", "        daily_productions = [0] * 品番数\n", "        daily_time = 0\n", "        \n", "        # 生産優先順位を決定\n", "        # 在庫が少ない、かつ将来の需要が大きい品番を優先\n", "        priority_queue = []\n", "        for i in range(品番数):\n", "            # 将来の総需要を計算\n", "            future_demand = sum(出荷数リスト[i] for d in range(look_ahead_period) if t + d < 期間)\n", "            # 在庫と将来需要のバランスを評価\n", "            score = (temp_inventory[i] - future_demand) / 収容数リスト[i]\n", "            priority_queue.append((score, i))\n", "            \n", "        priority_queue.sort()\n", "\n", "        # 優先度の高い品番から生産量を決定\n", "        for score, i in priority_queue:\n", "            setup_time = 30 if daily_productions[i] == 0 else 0\n", "            remaining_time = max_daily_work_time - daily_time\n", "            \n", "            if remaining_time <= setup_time:\n", "                break\n", "            \n", "            cycle_time_per_unit = サイクルタイムリスト[i] / 込め数リスト[i]\n", "            if cycle_time_per_unit == 0: continue\n", "            \n", "            max_producible_by_time = int((remaining_time - setup_time) / cycle_time_per_unit)\n", "            \n", "            # 在庫が十分か確認\n", "            if temp_inventory[i] >= 出荷数リスト[i] * 2: # 2日分の在庫があれば今回は生産しない\n", "                continue\n", "            \n", "            # 目標在庫水準（例: 3日分）に達するように生産量を計算\n", "            target_inventory = 出荷数リスト[i] * 1\n", "            production = max(0, target_inventory - temp_inventory[i])\n", "            production = min(production, max_producible_by_time)\n", "            \n", "            if production > 0:\n", "                daily_productions[i] = production\n", "                daily_time += setup_time + (production / 込め数リスト[i]) * サイクルタイムリスト[i]\n", "        \n", "        solution.append(daily_productions)\n", "        \n", "        for i in range(品番数):\n", "            temp_inventory[i] += daily_productions[i] - 出荷数リスト[i]\n", "            \n", "    return solution\n", "\n", "def get_neighbors(current_solution):\n", "    \"\"\"より多様な近傍解を生成する関数\"\"\"\n", "    neighbors = []\n", "    num_neighbors = 30\n", "    \n", "    for _ in range(num_neighbors):\n", "        neighbor = copy.deepcopy(current_solution)\n", "        operation_type = random.choice(['swap', 'shift_time', 'adjust_amount', 'consolidate'])\n", "        \n", "        if operation_type == 'swap':\n", "            t1, t2 = random.randint(0, 期間 - 1), random.randint(0, 期間 - 1)\n", "            i1, i2 = random.randint(0, 品番数 - 1), random.randint(0, 品番数 - 1)\n", "            if (t1, i1) != (t2, i2):\n", "                neighbor[t1][i1], neighbor[t2][i2] = neighbor[t2][i2], neighbor[t1][i1]\n", "\n", "        elif operation_type == 'shift_time':\n", "            t_from = random.randint(0, 期間 - 1)\n", "            t_to = random.randint(0, 期間 - 1)\n", "            i = random.randint(0, 品番数 - 1)\n", "            if t_from != t_to:\n", "                production_amount = neighbor[t_from][i]\n", "                if production_amount > 0:\n", "                    neighbor[t_from][i] = 0\n", "                    neighbor[t_to][i] += production_amount\n", "\n", "        elif operation_type == 'adjust_amount':\n", "            t = random.randint(0, 期間 - 1)\n", "            i = random.randint(0, 品番数 - 1)\n", "            change = random.randint(-100, 100)\n", "            neighbor[t][i] = max(0, neighbor[t][i] + change)\n", "\n", "        elif operation_type == 'consolidate':\n", "            i = random.randint(0, 品番数 - 1)\n", "            t_target = random.randint(0, 期間 - 1)\n", "            total_production_for_part = sum(neighbor[t][i] for t in range(期間))\n", "            for t in range(期間):\n", "                neighbor[t][i] = 0\n", "            neighbor[t_target][i] = total_production_for_part\n", "            \n", "        neighbors.append(neighbor)\n", "    return neighbors\n", "\n", "def simulated_annealing(initial_solution, current_initial_inventory, history_list):\n", "    \"\"\"\n", "    焼きなまし法を実行する関数\n", "    \"\"\"\n", "    current_solution = initial_solution\n", "    current_cost, _ = evaluate(current_solution, current_initial_inventory)\n", "    best_solution = current_solution\n", "    best_cost = current_cost\n", "    \n", "    # 焼きなまし法のパラメータ\n", "    T = 100000.0  # 初期温度\n", "    alpha = 0.995 # 冷却率\n", "    \n", "    iteration_limit = 1000 # 繰り返し回数上限\n", "\n", "    for i in range(iteration_limit):\n", "        if T < 1e-6: # 温度が十分に下がったら終了\n", "            break\n", "\n", "        # 近傍解を1つ生成（より効率的な近傍解生成に切り替え）\n", "        neighbor = get_neighbors(current_solution)[0]\n", "        neighbor_cost, _ = evaluate(neighbor, current_initial_inventory)\n", "        \n", "        delta_cost = neighbor_cost - current_cost\n", "\n", "        # 改善した場合は常に受け入れる\n", "        if delta_cost < 0:\n", "            current_solution = neighbor\n", "            current_cost = neighbor_cost\n", "            if current_cost < best_cost:\n", "                best_solution = current_solution\n", "                best_cost = current_cost\n", "        else:\n", "            # 悪化した場合は、確率で受け入れる\n", "            acceptance_probability = np.exp(-delta_cost / T)\n", "            if random.random() < acceptance_probability:\n", "                current_solution = neighbor\n", "                current_cost = neighbor_cost\n", "\n", "        # 温度を下げる\n", "        T *= alpha\n", "\n", "        # 履歴を記録\n", "        history_list.append(current_cost)\n", "            \n", "    return best_solution, best_cost\n", "\n", "def multi_start_sa(num_starts, current_initial_inventory):\n", "    \"\"\"多スタート焼きなまし法を実行する関数\"\"\"\n", "    best_solution_overall = None\n", "    best_cost_overall = float('inf')\n", "    search_histories = []\n", "\n", "    for i in range(num_starts):\n", "        print(f\"--- Start {i+1}/{num_starts} ---\")\n", "        initial_solution = generate_initial_solution(current_initial_inventory)\n", "        current_history = []\n", "        local_optimal_solution, local_optimal_cost = simulated_annealing(initial_solution, current_initial_inventory, current_history)\n", "        search_histories.append({'start_num': i + 1, 'history': current_history, 'final_cost': local_optimal_cost})\n", "        \n", "        if local_optimal_cost < best_cost_overall:\n", "            best_cost_overall = local_optimal_cost\n", "            best_solution_overall = local_optimal_solution\n", "            print(f\"  New best solution found with total cost: {best_cost_overall:.2f}\")\n", "            \n", "    return best_solution_overall, best_cost_overall, search_histories\n", "\n", "def simulate_production_schedule_simple(initial_inventory, 期間=20):\n", "    \"\"\"生産スケジュールをシミュレートする関数（簡易版）\"\"\"\n", "    global 品番数\n", "    品番数 = len(initial_inventory)\n", "    inventory = initial_inventory[:]\n", "    inventory_history = [[] for _ in range(品番数)]\n", "    \n", "    daily_regular_time = 8 * 60 * 2\n", "    max_daily_overtime = 2 * 60 * 2\n", "    max_daily_work_time = daily_regular_time + max_daily_overtime\n", "    \n", "    for t in range(期間):\n", "        daily_production_time = 0\n", "        daily_setup_count = 0\n", "        \n", "        for i in range(品番数):\n", "            demand = 出荷数リスト[i]\n", "            inventory[i] -= demand\n", "            \n", "            if inventory[i] < 0:\n", "                shortage = abs(inventory[i])\n", "                production = shortage\n", "                \n", "                if production > 0:\n", "                    daily_setup_count += 1\n", "                    setup_time = 30\n", "                    production_time = (production / 込め数リスト[i]) * サイクルタイムリスト[i]\n", "                    daily_production_time += production_time + setup_time\n", "                \n", "                inventory[i] += production\n", "            \n", "            inventory_history[i].append(max(0, inventory[i]))\n", "        \n", "        if daily_production_time > max_daily_work_time:\n", "            reduction_factor = max_daily_work_time / daily_production_time\n", "            for i in range(品番数):\n", "                if inventory[i] > 0:\n", "                    current_production = max(0, 出荷数リスト[i] - (initial_inventory[i] - inventory[i]))\n", "                    reduced_production = current_production * reduction_factor\n", "                    inventory[i] = initial_inventory[i] + reduced_production - 出荷数リスト[i] * (t + 1)\n", "                    inventory[i] = max(0, inventory[i])\n", "                    inventory_history[i][-1] = inventory[i]\n", "    return inventory_history\n", "\n", "def adjust_initial_inventory(初期在庫量リスト, 在庫コスト単価, 出荷遅れコスト単価, num_simulations=50, max_iterations=5):\n", "    \"\"\"初期在庫量を最適化する関数（簡易シミュレーション使用版）\"\"\"\n", "    s = 初期在庫量リスト[:]\n", "    \n", "    print(\"\\n=== 初期在庫水準の調整アルゴリズム開始 ===\")\n", "    \n", "    for iteration in range(max_iterations):\n", "        print(f\"--- 調整イテレーション {iteration+1} ---\")\n", "        inventory_distributions = [[] for _ in range(品番数)]\n", "        for _ in range(num_simulations):\n", "            inventory_history = simulate_production_schedule_simple(s)\n", "            for i in range(品番数):\n", "                inventory_distributions[i].extend(inventory_history[i])\n", "        \n", "        adjustments = [0] * 品番数\n", "        \n", "        for i in range(品番数):\n", "            if not inventory_distributions[i]:\n", "                continue\n", "            \n", "            inventory_counts = pd.Series(inventory_distributions[i]).value_counts(normalize=True).sort_index()\n", "            cumulative_distribution = inventory_counts.cumsum()\n", "            prob_target = 在庫コスト単価 / (在庫コスト単価 + 出荷遅れコスト単価)\n", "            \n", "            best_r = 0\n", "            for r in cumulative_distribution.index:\n", "                prob_at_r_minus_1 = cumulative_distribution.get(r - 1, 0)\n", "                if prob_at_r_minus_1 <= prob_target:\n", "                    best_r = r\n", "                else:\n", "                    break\n", "            \n", "            adjustments[i] = s[i] - best_r\n", "            \n", "        print(f\"  今回の調整量: {adjustments}\")\n", "        new_s = [s[i] - adjustments[i] for i in range(品番数)]\n", "        \n", "        if all(abs(adj) < 1 for adj in adjustments):\n", "            print(\"--- アルゴリズムが収束しました。---\")\n", "            return s\n", "            \n", "        s = new_s\n", "        print(f\"  更新後の初期在庫量: {s}\")\n", "        \n", "    print(\"--- 最大反復回数に到達しました。---\")\n", "    return s\n", "\n", "def plot_results(best_individual, initial_inventory, save_path=None):\n", "    \"\"\"結果をプロットする関数\"\"\"\n", "    global 品番数, 期間, 出荷数リスト, サイクルタイムリスト, 込め数リスト\n", "\n", "    print(\"\\n=== 結果のプロット ===\")\n", "    total_inventory_per_period = []\n", "    total_production_time_per_period = []\n", "    total_setup_times_per_period = []\n", "    total_shipment_delay_per_period = []\n", "    inventory = initial_inventory[:]\n", "    max_daily_work_time = (8 + 2) * 60 * 2\n", "    daily_regular_time = 8 * 60 * 2\n", "    \n", "    for t in range(期間):\n", "        daily_inventory = 0\n", "        daily_production_time = 0\n", "        daily_setup_times = 0\n", "        daily_shipment_delay = 0\n", "\n", "        for i in range(品番数):\n", "            production = best_individual[t][i]\n", "\n", "            if production > 0:\n", "                daily_setup_times += 1\n", "                setup_time = 30\n", "            else:\n", "                setup_time = 0\n", "\n", "            production_time = (production / 込め数リスト[i]) * サイクルタイムリスト[i]\n", "            daily_production_time += production_time + setup_time\n", "            inventory[i] += production - 出荷数リスト[i]\n", "\n", "            if inventory[i] < 0:\n", "                daily_shipment_delay += abs(inventory[i])\n", "                inventory[i] = 0\n", "            daily_inventory += inventory[i]\n", "\n", "        total_inventory_per_period.append(daily_inventory)\n", "        total_production_time_per_period.append(daily_production_time)\n", "        total_setup_times_per_period.append(daily_setup_times)\n", "        total_shipment_delay_per_period.append(daily_shipment_delay)\n", "\n", "    fig, axes = plt.subplots(2, 2, figsize=(16, 12))\n", "    periods = list(range(1, 期間 + 1))\n", "\n", "    axes[0, 0].bar(periods, total_inventory_per_period, color='skyblue', alpha=0.7)\n", "    axes[0, 0].set_title('各期間の総在庫量')\n", "    axes[0, 0].set_xlabel('期間')\n", "    axes[0, 0].set_ylabel('総在庫量 (個)')\n", "    axes[0, 0].grid(True, alpha=0.3)\n", "\n", "    axes[0, 1].bar(periods, total_production_time_per_period, color='lightgreen', alpha=0.7)\n", "    axes[0, 1].axhline(y=max_daily_work_time, color='red', linestyle='--', alpha=0.8, label=f'上限 ({max_daily_work_time}分)')\n", "    axes[0, 1].axhline(y=daily_regular_time, color='green', linestyle='--', alpha=0.8, label=f'定時 ({daily_regular_time}分)')\n", "    axes[0, 1].set_title('各期間の総生産時間')\n", "    axes[0, 1].set_xlabel('期間')\n", "    axes[0, 1].set_ylabel('総稼働時間 (分)')\n", "    axes[0, 1].grid(True, alpha=0.3)\n", "    axes[0, 1].legend()\n", "    \n", "    axes[1, 0].bar(periods, total_setup_times_per_period, color='salmon', alpha=0.7)\n", "    axes[1, 0].set_title('各期間の総段替え回数')\n", "    axes[1, 0].set_xlabel('期間')\n", "    axes[1, 0].set_ylabel('総段替え回数（回）')\n", "    axes[1, 0].grid(True, alpha=0.3)\n", "\n", "    axes[1, 1].bar(periods, total_shipment_delay_per_period, color='gold', alpha=0.7)\n", "    axes[1, 1].set_title('各期間の総出荷遅れ量')\n", "    axes[1, 1].set_xlabel('期間')\n", "    axes[1, 1].set_ylabel('総出荷遅れ量 (個)')\n", "    axes[1, 1].grid(True, alpha=0.3)\n", "\n", "    plt.tight_layout()\n", "    if save_path:\n", "        plt.savefig(save_path, dpi=300, bbox_inches='tight')\n", "        print(f\"プロットを保存: {save_path}\")\n", "    else:\n", "        plt.show()\n", "\n", "def plot_search_history(search_histories, save_path=None):\n", "    \"\"\"各スタートの探索過程（コストの推移）をプロットする関数\"\"\"\n", "    plt.figure(figsize=(12, 8))\n", "    for history_data in search_histories:\n", "        history = history_data['history']\n", "        start_num = history_data['start_num']\n", "        x_axis = range(len(history))\n", "        plt.plot(x_axis, history, label=f'スタート {start_num}')\n", "    plt.title('各スタートの焼きなまし法探索履歴')\n", "    plt.xlabel('ステップ数')\n", "    plt.ylabel('総コスト')\n", "    plt.grid(True, linestyle='--', alpha=0.6)\n", "    plt.legend(bbox_to_anchor=(1.05, 1), loc='upper left')\n", "    plt.tight_layout(rect=[0, 0, 0.85, 1])\n", "    if save_path:\n", "        plt.savefig(save_path, dpi=300, bbox_inches='tight')\n", "        print(f\"探索履歴プロットを保存: {save_path}\")\n", "    else:\n", "        plt.show()\n", "    plt.close()\n", "\n", "def process_single_file(file_path):\n", "    \"\"\"単一のCSVファイルを処理する関数\"\"\"\n", "    global 品番数, 期間\n", "    print(f\"\\n=== Processing {file_path} ===\")\n", "    \n", "    品番リスト, 出荷数リスト, 収容数リスト, サイクルタイムリスト, 込め数リスト, 初期在庫量リスト = read_csv(file_path)\n", "    品番数 = len(品番リスト)\n", "    期間 = 20\n", "    \n", "    adjusted_initial_inventory = adjust_initial_inventory(初期在庫量リスト, 在庫コスト単価, 出荷遅れコスト単価, num_simulations=50, max_iterations=3)\n", "    初期在庫量リスト[:] = adjusted_initial_inventory\n", "    \n", "    print(\"=== 多スタート焼きなまし法 スケジューリング ===\")\n", "    start_time = time.time()\n", "    num_starts = 5 # 試行回数を増やして探索を強化\n", "    best_solution, best_cost, search_histories = multi_start_sa(num_starts, 初期在庫量リスト)\n", "    calculation_time = time.time() - start_time\n", "    \n", "    if best_solution:\n", "        print(f\"\\n=== 最適化結果 ===\")\n", "        print(f\"最良個体の総コスト: {best_cost:.2f}\")\n", "        print(f\"計算時間: {calculation_time:.2f}秒\")\n", "        \n", "        base_name = os.path.splitext(os.path.basename(file_path))[0]\n", "        result_csv_path = f\"result/sa_results_{base_name}.csv\"\n", "        plot_path = f\"result/sa_results_{base_name}.png\"\n", "        \n", "        plot_search_history(search_histories)\n", "        plot_results(best_solution, 初期在庫量リスト, plot_path)\n", "        \n", "        return best_cost, calculation_time, base_name\n", "    else:\n", "        print(\"\\n解が見つかりませんでした\")\n", "        return None, None, None\n", "\n", "def main():\n", "    \"\"\"メイン実行関数 - 全CSVファイルを処理\"\"\"\n", "    data_folder = \"data\"\n", "    \n", "    if not os.path.exists(data_folder):\n", "        print(f\"'{data_folder}' フォルダが見つかりません。データファイルを配置してください。\")\n", "        return\n", "\n", "    csv_files = [f for f in os.listdir(data_folder) if f.endswith('.csv')]\n", "    \n", "    if not csv_files:\n", "        print(f\"'{data_folder}' フォルダにCSVファイルが見つかりません\")\n", "        return\n", "    \n", "    if not os.path.exists('result'):\n", "        os.makedirs('result')\n", "    \n", "    print(f\"見つかったCSVファイル: {csv_files}\")\n", "    \n", "    all_results = []\n", "    total_objective_value = 0\n", "    total_calculation_time = 0\n", "    \n", "    for csv_file in csv_files:\n", "        file_path = os.path.join(data_folder, csv_file)\n", "        objective_value, calc_time, file_name = process_single_file(file_path)\n", "        \n", "        if objective_value is not None:\n", "            all_results.append({\n", "                'ファイル名': file_name,\n", "                '目的関数値': objective_value,\n", "                '計算時間': calc_time\n", "            })\n", "            total_objective_value += objective_value\n", "            total_calculation_time += calc_time\n", "    \n", "    summary_df = pd.DataFrame(all_results)\n", "    \n", "    summary_row = pd.DataFrame({\n", "        'ファイル名': ['合計'],\n", "        '目的関数値': [total_objective_value],\n", "        '計算時間': [total_calculation_time]\n", "    })\n", "    summary_df = pd.concat([summary_df, summary_row], ignore_index=True)\n", "    \n", "    summary_csv_path = \"result/simulated_annealing_aggregate_results.csv\"\n", "    summary_df.to_csv(summary_csv_path, index=False, encoding='shift-jis')\n", "    print(f\"\\n集計結果をCSVファイルに保存: {summary_csv_path}\")\n", "    \n", "    print(f\"\\n=== 全体の集計結果 ===\")\n", "    print(f\"処理したファイル数: {len(all_results)}\")\n", "    print(f\"総目的関数値: {total_objective_value:.2f}\")\n", "    print(f\"総計算時間: {total_calculation_time:.2f}秒\")\n", "    if len(all_results) > 0:\n", "        print(f\"平均目的関数値: {total_objective_value/len(all_results):.2f}\")\n", "        print(f\"平均計算時間: {total_calculation_time/len(all_results):.2f}秒\")\n", "        \n", "if __name__ == \"__main__\":\n", "    main()"]}, {"cell_type": "markdown", "id": "026f47a0", "metadata": {}, "source": []}, {"cell_type": "markdown", "id": "44e88872", "metadata": {}, "source": ["平均して何％くらい悪い  \n", "悪くなった例をもっと調べる  \n", "悪くなった原因がなんなのか  \n", "もっと探索か初期解生成を工夫する  \n", "同じ品番，別の生産期間と入れ替える，5日幅とかで見てスワップ？  \n", "需要量は変動しないものとしている  \n", "生産量の上限は，需要数＊マシン品番数まで  \n", "グループ分け（引き当て）の問題もある  \n", "もう少し多スタートに計算時間かけても良いかも  \n", "線形計画のほうで，期間を切り取る（t=20のうち5ずつ実行とか）  \n", "ローリング法？  \n", "MIPの方で最終在庫を考慮してないから在庫コスト低いんじゃないかな  "]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.7"}}, "nbformat": 4, "nbformat_minor": 5}