import random
import numpy as np
import pandas as pd
import csv
import matplotlib.pyplot as plt
import japanize_matplotlib
import copy
import os
import time

# グローバル変数
在庫コスト単価 = 180
残業コスト単価 = 66.7
段替えコスト単価 = 400
出荷遅れコスト単価 = 500

定時 = 8 * 60 * 2
最大残業時間 = 2 * 60 * 2
期間 = 20

def read_csv(file_path):
    """CSVファイルを読み込む関数"""
    global 品番リスト, 出荷数リスト, 収容数リスト, サイクルタイムリスト, 込め数リスト, 初期在庫量リスト, 期間

    収容数辞書 = {}
    with open('収容数.csv', 'r', encoding='shift-jis') as capacity_file:
        capacity_reader = csv.reader(capacity_file)
        capacity_header = next(capacity_reader)
        for row in capacity_reader:
            if len(row) >= 2 and row[1].strip():
                品番 = row[0]  # 品番列
                収容数 = int(float(row[1]))
                収容数辞書[品番] = 収容数
    
    with open(file_path, 'r', encoding='shift-jis') as file:
        reader = csv.reader(file)
        header = next(reader)
        
        品番リスト = []
        出荷数リスト = []
        収容数リスト = []
        サイクルタイムリスト = []
        込め数リスト = []
        
        rows = list(reader)
        for row in rows:
            if len(row) == 0:
                continue
            
            # 個数を取得
            total_quantity = int(row[header.index("個数")])
            
            # 個数が200未満の場合はスキップ
            if total_quantity < 200:
                continue
            
            # 1日あたりの出荷数を計算（総期間で割る）
            daily_quantity = total_quantity / 期間
            
            品番リスト.append(row[header.index("素材品番")])
            出荷数リスト.append(daily_quantity)
            込め数リスト.append(int(float(row[header.index("込数")])))
            
            cycle_time_per_unit = float(row[header.index("サイクルタイム")]) / 60
            サイクルタイムリスト.append(cycle_time_per_unit)
            
            収容数リスト.append(収容数辞書.get(品番, 80))
            
        # 出荷数に基づいて初期在庫量（減少処理前）をランダムに設定
        初期在庫量リスト = []
        for shipment in 出荷数リスト:
            random_inventory = random.randint(int(shipment * 3), int(shipment * 5))
            初期在庫量リスト.append(random_inventory)
            
    return 品番リスト, 出荷数リスト, 収容数リスト, サイクルタイムリスト, 込め数リスト, 初期在庫量リスト


def simulate_production_schedule_simple():
    """生産スケジュールを簡易的にシミュレートする関数"""
    global 期間, 出荷数リスト, 込め数リスト, サイクルタイムリスト, 定時, 最大残業時間, 品番数, 初期在庫量リスト
    
    inventory = 初期在庫量リスト[:]
    inventory_history = [[] for _ in range(品番数)]
    
    max_daily_work_time = 定時 + 最大残業時間
    
    for t in range(期間):
        daily_production_time = 0
        daily_setup_count = 0
        
        # 各品番の需要を処理
        for i in range(品番数):
            demand = 出荷数リスト[i]
            inventory[i] -= demand
            
            # 在庫が不足する場合は生産
            if inventory[i] < 0:
                shortage = abs(inventory[i])
                # 不足分だけ生産
                production = shortage
                
                # 生産時間を計算
                if production > 0:
                    daily_setup_count += 1
                    setup_time = 30  # 段替え時間
                    production_time = (production / 込め数リスト[i]) * サイクルタイムリスト[i]
                    daily_production_time += production_time + setup_time
                
                inventory[i] += production
            
            # 在庫履歴に記録
            inventory_history[i].append(max(0, inventory[i]))
        
        # 稼働時間制約チェック
        if daily_production_time > max_daily_work_time:
            # 超過分を比例配分で削減
            reduction_factor = max_daily_work_time / daily_production_time
            for i in range(品番数):
                if inventory[i] > 0:
                    # 生産量を削減し、在庫を調整
                    current_production = max(0, 出荷数リスト[i] - (初期在庫量リスト[i] - inventory[i]))
                    reduced_production = current_production * reduction_factor
                    inventory[i] = 初期在庫量リスト[i] + reduced_production - 出荷数リスト[i] * (t + 1)
                    inventory[i] = max(0, inventory[i])
                    inventory_history[i][-1] = inventory[i]
    
    return inventory_history


def adjust_initial_inventory(num_simulations=50, max_iterations=3):
    """初期在庫量を最適化する関数"""
    global 初期在庫量リスト, 在庫コスト単価, 出荷遅れコスト単価, 品番数
    
    s = 初期在庫量リスト[:]
    
    print("\n=== 初期在庫調整アルゴリズムの開始 ===")
    
    for iteration in range(max_iterations):
        
        # 各在庫点について在庫量の分布を求める
        inventory_distributions = [[] for _ in range(品番数)]
        for _ in range(num_simulations):
            inventory_history = simulate_production_schedule_simple()
            for i in range(品番数):
                inventory_distributions[i].extend(inventory_history[i])
        
        adjustments = [0] * 品番数
        
        # 各在庫点について在庫量の最適調整量r^*を求める
        for i in range(品番数):
            if not inventory_distributions[i]:
                continue
            
            inventory_counts = pd.Series(inventory_distributions[i]).value_counts(normalize=True).sort_index()
            cumulative_distribution = inventory_counts.cumsum()
            
            # sum_{x <= r-1} f(x) <= h / (h + c)
            prob_target = 在庫コスト単価 / (在庫コスト単価 + 出荷遅れコスト単価)
            
            best_r = 0
            for r in cumulative_distribution.index:
                prob_at_r_minus_1 = cumulative_distribution.get(r - 1, 0)
                if prob_at_r_minus_1 <= prob_target:
                    best_r = r
                else:
                    break
            
            adjustments[i] = s[i] - best_r
        
        # すべての在庫点での初期在庫量をs:=s-r^*によって更新する
        new_s = [s[i] - adjustments[i] for i in range(品番数)]
        
        # 終了条件のチェック
        if all(abs(adj) < 1 for adj in adjustments):
            print("--- アルゴリズムが収束---")
            return s
            
        s = new_s
        print(f"  更新後の初期在庫量: {s}")
        
    print("--- 最大反復回数に到達---")
    return s


def generate_initial_solution(current_initial_inventory):
    """初期解を生成する関数"""
    global 品番数, 期間, 出荷数リスト, サイクルタイムリスト, 込め数リスト, 定時, 最大残業時間
    
    solution = []
    temp_inventory = current_initial_inventory[:]
    max_daily_work_time = 定時 + 最大残業時間
    
    # 全期間の総需要を計算
    total_demand = [sum(出荷数リスト) * 期間 for _ in range(品番数)]
    
    for t in range(期間):
        daily_productions = [0] * 品番数
        daily_time = 0
        
        # 在庫が不足している品番を優先して生産
        priority_queue = []
        for i in range(品番数):
            remaining_inventory = temp_inventory[i] - 出荷数リスト[i]
            priority_queue.append((remaining_inventory, i))
        priority_queue.sort()
        
        for remaining_inventory, i in priority_queue:
            setup_time = 30 if daily_productions[i] == 0 else 0 # 生産があるときは段替え30分がある
            remaining_time = max_daily_work_time - daily_time
            
            if remaining_time <= setup_time:
                break
            
            cycle_time_per_unit = サイクルタイムリスト[i] / 込め数リスト[i]
            if cycle_time_per_unit == 0: continue
            
            max_producible_by_time = int((remaining_time - setup_time) / cycle_time_per_unit)
            
            # 在庫が不十分な場合は、その不足分+今後の需要5日分を生産
            target_production = 0
            if remaining_inventory < 0:
                target_production = abs(remaining_inventory) + random.randint(0, int(出荷数リスト[i] * 1.5))
            else:
                # 在庫が十分な場合は、今後の需要5日分を生産
                target_production = random.randint(0, int(出荷数リスト[i] * 1.5))
                
            production = min(target_production, max_producible_by_time)
            
            # 生産量のチェック
            if production > 0:
                daily_productions[i] = production
                daily_time += setup_time + (production / 込め数リスト[i]) * サイクルタイムリスト[i]
        
        solution.append(daily_productions)
        
        for i in range(品番数):
            temp_inventory[i] += daily_productions[i] - 出荷数リスト[i]
            
    return solution


def evaluate(solution, current_initial_inventory):
    """総コスト（在庫、残業、段替え、出荷遅れ）を最小化する評価関数"""
    global 品番数, 期間, 出荷数リスト, サイクルタイムリスト, 込め数リスト, 定時, 最大残業時間
    global 出荷遅れコスト単価, 在庫コスト単価, 段替えコスト単価, 残業コスト単価
    
    # 最小化コスト
    total_inventory_cost = 0
    total_overtime_cost = 0
    total_setup_cost = 0
    total_shipment_delay_cost = 0
    
    inventory = current_initial_inventory[:]
    inventory_history = [[] for _ in range(品番数)]
    
    for t in range(期間):
        daily_time = 0
        daily_setup_count = 0
        
        for i in range(品番数):
            production = solution[t][i]
            
            # 生産があるとき、段替え30分がある
            if production > 0:
                daily_setup_count += 1
                setup_time = 30
            else:
                setup_time = 0
            
            production_time = (production / 込め数リスト[i]) * サイクルタイムリスト[i]
            daily_time += production_time + setup_time
            
            inventory[i] += production - 出荷数リスト[i]
            inventory_history[i].append(inventory[i])
            
            # 出荷遅れコスト
            if inventory[i] < 0:
                shortage_amount = abs(inventory[i])
                total_shipment_delay_cost += 出荷遅れコスト単価 * shortage_amount
            
            # 在庫コスト
            if inventory[i] > 0:
                total_inventory_cost += (inventory[i] / 収容数リスト[i]) * 在庫コスト単価
        
        # 段替えコスト
        total_setup_cost += 段替えコスト単価 * daily_setup_count
        
        # 残業コスト
        if daily_time > 定時:
            overtime = daily_time - 定時
            total_overtime_cost += 残業コスト単価 * overtime
        
        # 最大稼働時間超過ペナルティ
        if daily_time > 定時 + 最大残業時間:
            work_time_penalty = (daily_time - (定時 + 最大残業時間)) * (残業コスト単価 * 1000)
            total_overtime_cost += work_time_penalty
            
    total_cost = total_inventory_cost + total_overtime_cost + total_setup_cost + total_shipment_delay_cost
    
    return total_cost, inventory_history


def get_neighbors(current_solution):
    neighbors = []
    num_neighbors = 30 # 生成する近傍解の数
    
    for _ in range(num_neighbors):
        neighbor = copy.deepcopy(current_solution)
        
        # 近傍探索のタイプをランダムに選択
        operation_type = random.choice(['swap', 'shift_time', 'adjust_amount', 'consolidate'])
        
        if operation_type == 'swap':
            # 2つの生産量を入れ替える
            t1, t2 = random.randint(0, 期間 - 1), random.randint(0, 期間 - 1)
            i1, i2 = random.randint(0, 品番数 - 1), random.randint(0, 品番数 - 1)
            
            if (t1, i1) != (t2, i2):
                neighbor[t1][i1], neighbor[t2][i2] = neighbor[t2][i2], neighbor[t1][i1]

        elif operation_type == 'shift_time':
            # ある期間の生産量を別の期間にシフトする
            t_from = random.randint(0, 期間 - 1)
            t_to = random.randint(0, 期間 - 1)
            i = random.randint(0, 品番数 - 1)
            
            if t_from != t_to:
                production_amount = neighbor[t_from][i]
                if production_amount > 0:
                    neighbor[t_from][i] = 0
                    neighbor[t_to][i] += production_amount

        elif operation_type == 'adjust_amount':
            # 特定の生産量を増減させる
            t = random.randint(0, 期間 - 1)
            i = random.randint(0, 品番数 - 1)
            
            change = random.randint(-1000, 1000)
            neighbor[t][i] = max(0, neighbor[t][i] + change)

        elif operation_type == 'consolidate':
            # 同じ品番の生産を特定の期間に集約する
            i = random.randint(0, 品番数 - 1)
            t_target = random.randint(0, 期間 - 1)
            
            total_production_for_part = 0
            for t in range(期間):
                if t != t_target:
                    total_production_for_part += neighbor[t][i]
                    neighbor[t][i] = 0
            
            neighbor[t_target][i] += total_production_for_part
            
        neighbors.append(neighbor)
        
    return neighbors


def local_search(initial_solution, current_initial_inventory, history_list):
    """ローカルサーチを実行する関数"""
    current_solution = initial_solution
    current_cost, _ = evaluate(current_solution, current_initial_inventory)
    
    # 初期解のコストを記録
    history_list.append(current_cost)
    
    while True:
        neighbors = get_neighbors(current_solution)
        best_neighbor = None
        best_neighbor_cost = float('inf')
        
        for neighbor in neighbors:
            neighbor_cost, _ = evaluate(neighbor, current_initial_inventory)
            if neighbor_cost < best_neighbor_cost:
                best_neighbor = neighbor
                best_neighbor_cost = neighbor_cost
        
        if best_neighbor_cost < current_cost:
            current_solution = best_neighbor
            current_cost = best_neighbor_cost
            # コストが改善されたら記録
            history_list.append(current_cost)
        else:
            break
            
    return current_solution, current_cost


def multi_start_local_search(num_starts, current_initial_inventory):
    """多スタートローカルサーチを実行する関数"""
    best_solution_overall = None
    best_cost_overall = float('inf')
    
    # 各スタートの探索履歴を保存するリスト
    search_histories = []

    for i in range(num_starts):
        print(f"--- スタート点 {i+1}/{num_starts} ---")
        
        initial_solution = generate_initial_solution(current_initial_inventory)
        
        # 今回のスタートの探索履歴を格納するリスト
        current_history = []
        
        local_optimal_solution, local_optimal_cost = local_search(initial_solution, current_initial_inventory, current_history)
        
        # 履歴を保存
        search_histories.append({'start_num': i + 1, 'history': current_history, 'final_cost': local_optimal_cost})
        
        if local_optimal_cost < best_cost_overall:
            best_cost_overall = local_optimal_cost
            best_solution_overall = local_optimal_solution
            print(f"  最良の総コスト: {best_cost_overall:.2f}")
            
    return best_solution_overall, best_cost_overall, search_histories


def plot_results(best_individual, save_path=None):
    """結果をプロットする関数"""
    global 品番数, 期間, 品番リスト, 出荷数リスト, 初期在庫量リスト, サイクルタイムリスト, 込め数リスト, 定時, 最大残業時間
    
    total_inventory_per_period = []
    total_production_time_per_period = []
    total_setup_times_per_period = []
    total_shipment_delay_per_period = []

    inventory = 初期在庫量リスト[:]
    max_daily_work_time = 定時 + 最大残業時間
    daily_regular_time = 定時
    
    for t in range(期間):
        daily_inventory = 0
        daily_production_time = 0
        daily_setup_times = 0
        daily_shipment_delay = 0

        for i in range(品番数):
            production = best_individual[t][i]

            if production > 0:
                daily_setup_times += 1
                setup_time = 30
            else:
                setup_time = 0

            production_time = (production / 込め数リスト[i]) * サイクルタイムリスト[i]
            daily_production_time += production_time + setup_time

            inventory[i] += production - 出荷数リスト[i]

            if inventory[i] < 0:
                daily_shipment_delay += abs(inventory[i])
                inventory[i] = 0

            daily_inventory += inventory[i]

        total_inventory_per_period.append(daily_inventory)
        total_production_time_per_period.append(daily_production_time)
        total_setup_times_per_period.append(daily_setup_times)
        total_shipment_delay_per_period.append(daily_shipment_delay)

    fig, axes = plt.subplots(2, 2, figsize=(16, 12))
    periods = list(range(1, 期間 + 1))

    # 1. 各期間の総在庫量
    axes[0, 0].bar(periods, total_inventory_per_period, color='skyblue', alpha=0.7)
    axes[0, 0].set_title('各期間の総在庫量')
    axes[0, 0].set_xlabel('期間')
    axes[0, 0].set_ylabel('総在庫量 (個)')
    axes[0, 0].grid(True, alpha=0.3)
    if total_inventory_per_period:
        axes[0, 0].set_ylim(0, max(total_inventory_per_period) * 1.2)

    # 2. 各期間の総生産時間＋制限ライン
    axes[0, 1].bar(periods, total_production_time_per_period, color='lightgreen', alpha=0.7)
    axes[0, 1].axhline(y=max_daily_work_time, color='red', linestyle='--', alpha=0.8, label=f'上限 ({max_daily_work_time}分)')
    axes[0, 1].axhline(y=daily_regular_time, color='green', linestyle='--', alpha=0.8, label=f'定時 ({daily_regular_time}分)')
    axes[0, 1].set_title('各期間の総生産時間')
    axes[0, 1].set_xlabel('期間')
    axes[0, 1].set_ylabel('総稼働時間 (分)')
    axes[0, 1].grid(True, alpha=0.3)
    if total_production_time_per_period:
        axes[0, 1].set_ylim(0, max(max_daily_work_time, max(total_production_time_per_period)) * 1.2)
    axes[0, 1].legend()
    
    # 3. 各期間の総段替え回数
    axes[1, 0].bar(periods, total_setup_times_per_period, color='salmon', alpha=0.7)
    axes[1, 0].set_title('各期間の総段替え回数')
    axes[1, 0].set_xlabel('期間')
    axes[1, 0].set_ylabel('総段替え回数（回）')
    axes[1, 0].grid(True, alpha=0.3)
    if total_setup_times_per_period:
        axes[1, 0].set_ylim(0, max(total_setup_times_per_period) * 1.2)

    # 4. 各期間の総出荷遅れ量
    axes[1, 1].bar(periods, total_shipment_delay_per_period, color='gold', alpha=0.7)
    axes[1, 1].set_title('各期間の総出荷遅れ量')
    axes[1, 1].set_xlabel('期間')
    axes[1, 1].set_ylabel('総出荷遅れ量 (個)')
    axes[1, 1].grid(True, alpha=0.3)
    if total_shipment_delay_per_period:
        axes[1, 1].set_ylim(0, max(total_shipment_delay_per_period) * 1.2)

    plt.tight_layout()
    
    if save_path:
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        print(f"プロットを保存: {save_path}")
    else:
        plt.show()

    time_violations = sum(1 for x in total_production_time_per_period if x > daily_regular_time + max_daily_work_time)
    print(f"時間制約違反: {time_violations} 期間")

    return total_inventory_per_period, total_production_time_per_period, total_setup_times_per_period, total_shipment_delay_per_period


def plot_search_history(search_histories):
    """
    各スタートの探索過程をプロットする関数
    """
    plt.figure(figsize=(12, 8))
    
    for history_data in search_histories:
        history = history_data['history']
        start_num = history_data['start_num']
        
        x_axis = range(len(history))
        plt.plot(x_axis, history, marker='o', linestyle='-', label=f'スタート {start_num}')
        
    plt.title('各スタートのローカルサーチ探索履歴')
    plt.xlabel('改善ステップ数')
    plt.ylabel('総コスト')
    plt.grid(True, linestyle='--', alpha=0.6)
    plt.legend(bbox_to_anchor=(1.05, 1), loc='upper left')
    plt.tight_layout(rect=[0, 0, 0.85, 1])
    
    plt.show()


def process_single_file(file_path):
    """単一のCSVファイルを処理する関数"""
    global 品番数, 期間, 初期在庫量リスト
    
    print(f"\n=== 処理開始: {file_path} ===")
    
    # CSVファイルを読み込み
    品番リスト, 出荷数リスト, 収容数リスト, サイクルタイムリスト, 込め数リスト, 初期在庫量リスト = read_csv(file_path)
    品番数 = len(品番リスト)
    
    # 初期在庫量を調整
    adjusted_initial_inventory = adjust_initial_inventory(num_simulations=50, max_iterations=3)
    初期在庫量リスト = adjusted_initial_inventory
    
    print("=== 多スタートローカルサーチ スケジューリング ===")
    
    start_time = time.time()
    num_starts = 30
    
    best_solution, best_cost, search_histories = multi_start_local_search(num_starts, 初期在庫量リスト)
    calculation_time = time.time() - start_time
    
    if best_solution:
        print(f"\n=== 最適化結果 ===")
        print(f"最良個体の総コスト: {best_cost:.2f}")
        print(f"計算時間: {calculation_time:.2f}秒")
        
        base_name = os.path.splitext(os.path.basename(file_path))[0]
        result_csv_path = f"result/multi_start_results_{base_name}.csv"
        plot_path = f"result/multi_start_results_{base_name}.png"
        
        # 探索履歴をプロット
        plot_search_history(search_histories)
        
        return best_cost, calculation_time, base_name
    else:
        print("\n解が見つかりませんでした")
        return None, None, None


def main():
    """メイン実行関数"""
    data_folder = "data"
    
    # dataフォルダ内のすべてのCSVファイルを取得
    csv_files = [f for f in os.listdir(data_folder) if f.endswith('.csv')]
    
    if not csv_files:
        print("dataフォルダにCSVファイルが見つかりません")
        return
    
    print(f"見つかったCSVファイル: {csv_files}")
    
    # 結果を格納するリスト
    all_results = []
    total_objective_value = 0
    total_calculation_time = 0
    
    # 各CSVファイルを処理
    for csv_file in csv_files:
        file_path = os.path.join(data_folder, csv_file)
        objective_value, calc_time, file_name = process_single_file(file_path)
        
        if objective_value is not None:
            all_results.append({
                'ファイル名': file_name,
                '目的関数値': objective_value,
                '計算時間': calc_time
            })
            total_objective_value += objective_value
            total_calculation_time += calc_time
    
    # 集計結果をDataFrameに変換
    summary_df = pd.DataFrame(all_results)
    
    # 合計行を追加
    summary_row = pd.DataFrame({
        'ファイル名': ['合計'],
        '目的関数値': [total_objective_value],
        '計算時間': [total_calculation_time]
    })
    summary_df = pd.concat([summary_df, summary_row], ignore_index=True)
    
    # 集計結果をCSVとして保存
    summary_csv_path = "result/multi_start_aggregate_results.csv"
    summary_df.to_csv(summary_csv_path, index=False)
    print(f"\n集計結果をCSVファイルに保存: {summary_csv_path}")
    
    # 結果の要約を表示
    print(f"\n=== 全体の集計結果 ===")
    print(f"処理したファイル数: {len(all_results)}")
    print(f"総目的関数値: {total_objective_value:.2f}")
    print(f"総計算時間: {total_calculation_time:.2f}秒")
    if len(all_results) > 0:
        print(f"平均目的関数値: {total_objective_value/len(all_results):.2f}")
        print(f"平均計算時間: {total_calculation_time/len(all_results):.2f}秒")
