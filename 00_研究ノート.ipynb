{"cells": [{"cell_type": "markdown", "id": "4fa47832", "metadata": {}, "source": ["## 先行研究\n", "期末在庫量の分布型 : 生産・在庫システムの研究(1)（平川）  \n", "URL：https://www.jstage.jst.go.jp/article/jimapre/26/1/26_KJ00001728234/_article/-char/ja/  \n", "$t$期で$I_{it}$が負になった時，$t+1$期に持ち越される．  \n", "\n", "残業生産を考慮したロットサイズ決定問題に対するアニーリング・ヒューリスティック（森川・中村）  \n", "URL：https://www.jstage.jst.go.jp/article/kikaic1979/59/558/59_558_611/_article/-char/ja/  \n", "$I_{i0}=0$としている．  \n", "\n", "SCMにおける平準化生産・発注の最適化とその効果に関する研究（田村・大野）  \n", "URL: https://www.jstage.jst.go.jp/article/kikaic/79/798/79_162/_pdf  \n", "\n", "シミュレーションによる最適在庫水準決定アルゴリズムの性能評価に関する研究（田村）  \n", "URL: https://www.jstage.jst.go.jp/article/jsmemsd/2015/0/2015_99/_pdf.  \n", "最適な$I_{i0}$を求めるためのアルゴリズムを提案している．"]}, {"cell_type": "markdown", "id": "c3d4ff3d", "metadata": {}, "source": []}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.7"}}, "nbformat": 4, "nbformat_minor": 5}