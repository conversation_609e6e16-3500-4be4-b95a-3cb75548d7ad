{"cells": [{"cell_type": "code", "execution_count": 1, "id": "6e757c78", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["見つかったCSVファイル: ['D36.csv']\n", "\n", "=== Processing data/D36.csv ===\n", "\n", "=== 初期在庫水準の調整アルゴリズム開始 ===\n", "--- 調整イテレーション 1 ---\n", "  今回の調整量: [163.63636363636363, 351.81818181818176, 30.909090909090907, 42.54545454545455, 500.909090909091, 1130.181818181818]\n", "  更新後の初期在庫量: [654.3636363636364, 1291.1818181818182, 65.0909090909091, 102.45454545454545, 1092.090909090909, 2339.818181818182]\n", "--- 調整イテレーション 2 ---\n", "  今回の調整量: [163.63636363636363, 351.81818181818176, 30.909090909090907, 42.54545454545455, 500.909090909091, 1130.1818181818182]\n", "  更新後の初期在庫量: [490.72727272727275, 939.3636363636365, 34.18181818181819, 59.90909090909091, 591.181818181818, 1209.6363636363637]\n", "--- 調整イテレーション 3 ---\n", "  今回の調整量: [163.63636363636363, 351.81818181818176, 30.90909090909091, 42.54545454545455, 500.90909090909093, 1130.1818181818182]\n", "  更新後の初期在庫量: [327.0909090909091, 587.5454545454547, 3.272727272727277, 17.36363636363636, 90.27272727272708, 79.4545454545455]\n", "--- 最大反復回数に到達しました。---\n", "=== 多スタートローカルサーチ スケジューリング ===\n", "--- Start 1/30 ---\n", "  New best solution found with total cost: 140946.82\n", "--- Start 2/30 ---\n", "  New best solution found with total cost: 61498.64\n", "--- Start 3/30 ---\n", "--- Start 4/30 ---\n", "--- Start 5/30 ---\n", "--- Start 6/30 ---\n", "--- Start 7/30 ---\n", "--- Start 8/30 ---\n", "  New best solution found with total cost: 54787.73\n", "--- Start 9/30 ---\n", "--- Start 10/30 ---\n", "--- Start 11/30 ---\n", "--- Start 12/30 ---\n", "--- Start 13/30 ---\n", "--- Start 14/30 ---\n", "--- Start 15/30 ---\n", "--- Start 16/30 ---\n", "--- Start 17/30 ---\n", "--- Start 18/30 ---\n", "--- Start 19/30 ---\n", "--- Start 20/30 ---\n", "--- Start 21/30 ---\n", "--- Start 22/30 ---\n", "--- Start 23/30 ---\n", "--- Start 24/30 ---\n", "--- Start 25/30 ---\n", "--- Start 26/30 ---\n", "--- Start 27/30 ---\n", "--- Start 28/30 ---\n", "--- Start 29/30 ---\n", "--- Start 30/30 ---\n", "\n", "=== 最適化結果 ===\n", "最良個体の総コスト: 54787.73\n", "計算時間: 0.02秒\n", "結果をCSVファイルに保存: result/multi_start_results_D36.csv\n", "\n", "=== 結果のプロット ===\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/var/folders/_p/gpx29nx95pn__7720hyny3sm0000gn/T/ipykernel_40978/356469600.py:520: UserWarning: Attempting to set identical low and high ylims makes transformation singular; automatically expanding.\n", "  axes[1, 1].set_ylim(0, max(total_shipment_delay_per_period) * 1.2)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["プロットを保存: result/multi_start_results_D36.png\n", "時間制約違反: 0 期間\n", "\n", "集計結果をCSVファイルに保存: result/multi_start_aggregate_results.csv\n", "\n", "=== 全体の集計結果 ===\n", "処理したファイル数: 1\n", "総目的関数値: 54787.73\n", "総計算時間: 0.02秒\n", "平均目的関数値: 54787.73\n", "平均計算時間: 0.02秒\n"]}, {"data": {"image/png": "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", "text/plain": ["<Figure size 1600x1200 with 4 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["import random\n", "import numpy as np\n", "import pandas as pd\n", "import csv\n", "import matplotlib.pyplot as plt\n", "import japanize_matplotlib\n", "import copy\n", "\n", "# グローバル変数\n", "品番リスト = []\n", "出荷数リスト = []\n", "収容数リスト = []\n", "サイクルタイムリスト = []\n", "込め数リスト = []\n", "初期在庫量リスト = []\n", "\n", "# コストとペナルティの係数\n", "在庫コスト単価 = 180\n", "残業コスト単価 = 66.7\n", "段替えコスト単価 = 400\n", "出荷遅れコスト単価 = 500\n", "\n", "定時 = 8 * 60 * 2\n", "最大残業時間 = 2 * 60 * 2\n", "段替え時間 = 30\n", "品切れ率の許容値 = 0.05\n", "\n", "def read_csv(file_path):\n", "    \"\"\"CSVファイルを読み込む関数\"\"\"\n", "    global 品番リスト, 出荷数リスト, 収容数リスト, サイクルタイムリスト, 込め数リスト, 初期在庫量リスト\n", "\n", "    収容数辞書 = {}\n", "    with open('収容数.csv', 'r', encoding='shift-jis') as capacity_file:\n", "        capacity_reader = csv.reader(capacity_file)\n", "        capacity_header = next(capacity_reader)\n", "        for row in capacity_reader:\n", "            if len(row) >= 2 and row[1].strip():  # Skip if second column is empty\n", "                品番 = row[0]  # 品番列\n", "                収容数 = int(float(row[1]))  # 収容数列\n", "                収容数辞書[品番] = 収容数\n", "    \n", "    with open(file_path, 'r', encoding='shift-jis') as file:\n", "        reader = csv.reader(file)\n", "        header = next(reader)\n", "        \n", "        品番リスト = []\n", "        出荷数リスト = []\n", "        収容数リスト = []\n", "        サイクルタイムリスト = []\n", "        込め数リスト = []\n", "        \n", "        # 期間数（日数）を定義\n", "        期間数 = 22\n", "        \n", "        rows = list(reader)\n", "        for row in rows:\n", "            if len(row) == 0:\n", "                continue\n", "            \n", "            # 個数を取得\n", "            total_quantity = int(row[header.index(\"個数\")])\n", "            \n", "            # 個数が200未満の場合はスキップ\n", "            if total_quantity < 200:\n", "                continue\n", "            \n", "            # 1日あたりの出荷数を計算（総期間で割る）\n", "            daily_quantity = total_quantity / 期間数\n", "            \n", "            品番リスト.append(row[header.index(\"素材品番\")])\n", "            出荷数リスト.append(daily_quantity)\n", "            込め数リスト.append(int(float(row[header.index(\"込数\")])))\n", "            \n", "            cycle_time_per_unit = float(row[header.index(\"サイクルタイム\")]) / 60\n", "            サイクルタイムリスト.append(cycle_time_per_unit)\n", "            \n", "            収容数リスト.append(収容数辞書.get(品番, 80))\n", "            \n", "    \n", "        # 出荷数に基づいて初期在庫量（処理前）をランダムに設定\n", "        初期在庫量リスト = []\n", "        for shipment in 出荷数リスト:\n", "            random_inventory = random.randint(int(shipment * 3), int(shipment * 5))\n", "            初期在庫量リスト.append(random_inventory)\n", "            \n", "    return 品番リスト, 出荷数リスト, 収容数リスト, サイクルタイムリスト, 込め数リスト, 初期在庫量リスト\n", "\n", "def evaluate(solution, current_initial_inventory):\n", "    \"\"\"総コスト（在庫、残業、段替え、出荷遅れ）を最小化する評価関数\"\"\"\n", "    \n", "    # 最小化コスト\n", "    total_inventory_cost = 0\n", "    total_overtime_cost = 0\n", "    total_setup_cost = 0\n", "    total_shipment_delay_cost = 0\n", "    \n", "    inventory = current_initial_inventory[:]\n", "    inventory_history = [[] for _ in range(品番数)]\n", "    \n", "    for t in range(期間):\n", "        daily_time = 0\n", "        daily_setup_count = 0\n", "        \n", "        for i in range(品番数):\n", "            production = solution[t][i]\n", "            \n", "            # 生産がある場合に段替えをする\n", "            if production > 0:\n", "                daily_setup_count += 1\n", "                setup_time = 30\n", "            else:\n", "                setup_time = 0\n", "            \n", "            production_time = (production / 込め数リスト[i]) * サイクルタイムリスト[i]\n", "            daily_time += production_time + setup_time\n", "            \n", "            inventory[i] += production - 出荷数リスト[i]\n", "            inventory_history[i].append(inventory[i])\n", "            \n", "            # 出荷遅れコスト\n", "            if inventory[i] < 0:\n", "                shortage_amount = abs(inventory[i])\n", "                total_shipment_delay_cost += 出荷遅れコスト単価 * shortage_amount\n", "            \n", "            # 在庫コスト\n", "            if inventory[i] > 0:\n", "                total_inventory_cost += (inventory[i] / 収容数リスト[i]) * 在庫コスト単価\n", "        \n", "        # 段替えコスト\n", "        total_setup_cost += 段替えコスト単価 * daily_setup_count\n", "        \n", "        # 残業コスト\n", "        if daily_time > 定時:\n", "            overtime = daily_time - 定時\n", "            total_overtime_cost += 残業コスト単価 * overtime\n", "        \n", "        # 最大稼働時間超過ペナルティ（違反する場合はコストに加算）\n", "        if daily_time > 定時 + 最大残業時間:\n", "            work_time_penalty = (daily_time - (定時 + 最大残業時間)) * (残業コスト単価 * 1000000)\n", "            total_overtime_cost += work_time_penalty\n", "            \n", "    total_cost = total_inventory_cost + total_overtime_cost + total_setup_cost + total_shipment_delay_cost\n", "    \n", "    return total_cost, inventory_history\n", "\n", "def generate_initial_solution(current_initial_inventory):\n", "    \"\"\"初期解を生成する関数\"\"\"\n", "    solution = []\n", "    temp_inventory = current_initial_inventory[:]\n", "    max_daily_work_time = (8 + 2) * 60 * 2\n", "    \n", "    # 全期間の総需要を計算\n", "    total_demand = [sum(出荷数リスト) * 期間 for _ in range(品番数)]\n", "    \n", "    for t in range(期間):\n", "        daily_productions = [0] * 品番数\n", "        daily_time = 0\n", "        \n", "        # 在庫が不足している品番と、生産することで在庫が大幅に増える品番を区別\n", "        priority_queue = []\n", "        for i in range(品番数):\n", "            # 在庫がマイナスになる品番は高優先度\n", "            remaining_inventory = temp_inventory[i] - 出荷数リスト[i]\n", "            priority_queue.append((remaining_inventory, i))\n", "            \n", "        priority_queue.sort() # 在庫が少ない順に並べる\n", "        \n", "        for remaining_inventory, i in priority_queue:\n", "            setup_time = 30 if daily_productions[i] == 0 else 0\n", "            remaining_time = max_daily_work_time - daily_time\n", "            \n", "            if remaining_time <= setup_time:\n", "                break\n", "            \n", "            cycle_time_per_unit = サイクルタイムリスト[i] / 込め数リスト[i]\n", "            if cycle_time_per_unit == 0: continue\n", "            \n", "            max_producible_by_time = int((remaining_time - setup_time) / cycle_time_per_unit)\n", "            \n", "            # 在庫がマイナスになる場合、その不足分を補う\n", "            target_production = 0\n", "            if remaining_inventory < 0:\n", "                target_production = abs(remaining_inventory)\n", "            else:\n", "                # 在庫が十分な場合は、今後の需要を見越して少しだけ生産\n", "                target_production = random.randint(0, int(出荷数リスト[i] * 1.5))\n", "                \n", "            production = min(target_production, max_producible_by_time)\n", "            \n", "            # 生産量が妥当な範囲内かチェック\n", "            if production > 0:\n", "                daily_productions[i] = production\n", "                daily_time += setup_time + (production / 込め数リスト[i]) * サイクルタイムリスト[i]\n", "        \n", "        solution.append(daily_productions)\n", "        \n", "        for i in range(品番数):\n", "            temp_inventory[i] += daily_productions[i] - 出荷数リスト[i]\n", "            \n", "    return solution\n", "\n", "def get_neighbors(current_solution):\n", "    neighbors = []\n", "    \n", "    # 2つの生産量を入れ替える\n", "    for _ in range(5):\n", "        neighbor = copy.deepcopy(current_solution)\n", "        t1, t2 = random.randint(0, 期間 - 1), random.randint(0, 期間 - 1)\n", "        i1, i2 = random.randint(0, 品番数 - 1), random.randint(0, 品番数 - 1)\n", "        \n", "        if (t1, i1) != (t2, i2):\n", "            neighbor[t1][i1], neighbor[t2][i2] = neighbor[t2][i2], neighbor[t1][i1]\n", "            neighbors.append(neighbor)\n", "            \n", "    # 特定の生産量を増減させる\n", "    for _ in range(5):\n", "        neighbor = copy.deepcopy(current_solution)\n", "        t = random.randint(0, 期間 - 1)\n", "        i = random.randint(0, 品番数 - 1)\n", "        \n", "        change = random.randint(-50, 50)\n", "        neighbor[t][i] = max(0, neighbor[t][i] + change)\n", "        neighbors.append(neighbor)\n", "        \n", "    return neighbors\n", "\n", "\n", "\"\"\"def get_neighbors(current_solution):\n", "    neighbors = []\n", "    num_neighbors = 10 # 生成する近傍解の数を増やす\n", "    \n", "    for _ in range(num_neighbors):\n", "        neighbor = copy.deepcopy(current_solution)\n", "        \n", "        # 近傍探索のタイプをランダムに選択\n", "        operation_type = random.choice(['swap', 'shift_time', 'adjust_amount', 'consolidate'])\n", "        \n", "        if operation_type == 'swap':\n", "            # 2つの生産量を入れ替える (従来のロジック)\n", "            t1, t2 = random.randint(0, 期間 - 1), random.randint(0, 期間 - 1)\n", "            i1, i2 = random.randint(0, 品番数 - 1), random.randint(0, 品番数 - 1)\n", "            \n", "            if (t1, i1) != (t2, i2):\n", "                neighbor[t1][i1], neighbor[t2][i2] = neighbor[t2][i2], neighbor[t1][i1]\n", "\n", "        elif operation_type == 'shift_time':\n", "            # ある期間の生産量を別の期間にシフトする\n", "            t_from = random.randint(0, 期間 - 1)\n", "            t_to = random.randint(0, 期間 - 1)\n", "            i = random.randint(0, 品番数 - 1)\n", "            \n", "            if t_from != t_to:\n", "                production_amount = neighbor[t_from][i]\n", "                if production_amount > 0:\n", "                    neighbor[t_from][i] = 0\n", "                    neighbor[t_to][i] += production_amount\n", "\n", "        elif operation_type == 'adjust_amount':\n", "            # 特定の生産量を増減させる (従来のロジックに似ているが、調整幅を広げる)\n", "            t = random.randint(0, 期間 - 1)\n", "            i = random.randint(0, 品番数 - 1)\n", "            \n", "            change = random.randint(-100, 100)\n", "            neighbor[t][i] = max(0, neighbor[t][i] + change)\n", "\n", "        elif operation_type == 'consolidate':\n", "            # 同じ品番の生産を特定の期間に集約する\n", "            i = random.randint(0, 品番数 - 1)\n", "            t_target = random.randint(0, 期間 - 1)\n", "            \n", "            total_production_for_part = 0\n", "            for t in range(期間):\n", "                if t != t_target:\n", "                    total_production_for_part += neighbor[t][i]\n", "                    neighbor[t][i] = 0\n", "            \n", "            neighbor[t_target][i] += total_production_for_part\n", "            \n", "        neighbors.append(neighbor)\n", "        \n", "    return neighbors\"\"\"\n", "\n", "def local_search(initial_solution, current_initial_inventory):\n", "    \"\"\"ローカルサーチを実行する関数\"\"\"\n", "    current_solution = initial_solution\n", "    current_cost, _ = evaluate(current_solution, current_initial_inventory)\n", "    \n", "    while True:\n", "        neighbors = get_neighbors(current_solution)\n", "        best_neighbor = None\n", "        best_neighbor_cost = float('inf')\n", "        \n", "        for neighbor in neighbors:\n", "            neighbor_cost, _ = evaluate(neighbor, current_initial_inventory)\n", "            if neighbor_cost < best_neighbor_cost:\n", "                best_neighbor = neighbor\n", "                best_neighbor_cost = neighbor_cost\n", "        \n", "        if best_neighbor_cost < current_cost:\n", "            current_solution = best_neighbor\n", "            current_cost = best_neighbor_cost\n", "        else:\n", "            break\n", "            \n", "    return current_solution, current_cost\n", "\n", "def multi_start_local_search(num_starts, current_initial_inventory):\n", "    \"\"\"多スタートローカルサーチを実行する関数\"\"\"\n", "    best_solution_overall = None\n", "    best_cost_overall = float('inf')\n", "    \n", "    for i in range(num_starts):\n", "        print(f\"--- Start {i+1}/{num_starts} ---\")\n", "        \n", "        initial_solution = generate_initial_solution(current_initial_inventory)\n", "        \n", "        local_optimal_solution, local_optimal_cost = local_search(initial_solution, current_initial_inventory)\n", "        \n", "        if local_optimal_cost < best_cost_overall:\n", "            best_cost_overall = local_optimal_cost\n", "            best_solution_overall = local_optimal_solution\n", "            print(f\"  New best solution found with total cost: {best_cost_overall:.2f}\")\n", "            \n", "    return best_solution_overall, best_cost_overall\n", "\n", "def simulate_production_schedule_simple(initial_inventory, 期間=20):\n", "    \"\"\"生産スケジュールをシミュレートする関数（簡易版）\"\"\"\n", "    品番数 = len(initial_inventory)\n", "    inventory = initial_inventory[:]\n", "    inventory_history = [[] for _ in range(品番数)]\n", "    \n", "    daily_regular_time = 8 * 60 * 2  # 1日の通常稼働時間（分）\n", "    max_daily_overtime = 2 * 60 * 2  # 1日の最大残業時間（分）\n", "    max_daily_work_time = daily_regular_time + max_daily_overtime\n", "    \n", "    for t in range(期間):\n", "        daily_production_time = 0\n", "        daily_setup_count = 0\n", "        \n", "        # 各品番の需要を処理\n", "        for i in range(品番数):\n", "            demand = 出荷数リスト[i]\n", "            inventory[i] -= demand\n", "            \n", "            # 在庫が不足する場合は生産\n", "            if inventory[i] < 0:\n", "                shortage = abs(inventory[i])\n", "                # 生産量を決定（不足分を補う）\n", "                production = shortage\n", "                \n", "                # 生産時間を計算\n", "                if production > 0:\n", "                    daily_setup_count += 1\n", "                    setup_time = 30  # 段替え時間\n", "                    production_time = (production / 込め数リスト[i]) * サイクルタイムリスト[i]\n", "                    daily_production_time += production_time + setup_time\n", "                \n", "                inventory[i] += production\n", "            \n", "            # 在庫履歴に記録\n", "            inventory_history[i].append(max(0, inventory[i]))\n", "        \n", "        # 稼働時間制約チェック（制約違反の場合は一部生産を削減）\n", "        if daily_production_time > max_daily_work_time:\n", "            # 簡易的な調整：超過分を比例配分で削減\n", "            reduction_factor = max_daily_work_time / daily_production_time\n", "            for i in range(品番数):\n", "                if inventory[i] > 0:\n", "                    # 生産量を削減し、在庫を調整\n", "                    current_production = max(0, 出荷数リスト[i] - (initial_inventory[i] - inventory[i]))\n", "                    reduced_production = current_production * reduction_factor\n", "                    inventory[i] = initial_inventory[i] + reduced_production - 出荷数リスト[i] * (t + 1)\n", "                    inventory[i] = max(0, inventory[i])\n", "                    inventory_history[i][-1] = inventory[i]\n", "    \n", "    return inventory_history\n", "\n", "def adjust_initial_inventory(初期在庫量リスト, 在庫コスト単価, 出荷遅れコスト単価, num_simulations=50, max_iterations=5):\n", "    \"\"\"初期在庫量を最適化する関数（簡易シミュレーション使用版）\"\"\"\n", "    s = 初期在庫量リスト[:]\n", "    \n", "    print(\"\\n=== 初期在庫水準の調整アルゴリズム開始 ===\")\n", "    \n", "    for iteration in range(max_iterations):\n", "        print(f\"--- 調整イテレーション {iteration+1} ---\")\n", "        \n", "        # 各在庫点について在庫量の分布を求める\n", "        inventory_distributions = [[] for _ in range(品番数)]\n", "        for _ in range(num_simulations):\n", "            inventory_history = simulate_production_schedule_simple(s)\n", "            for i in range(品番数):\n", "                inventory_distributions[i].extend(inventory_history[i])\n", "        \n", "        adjustments = [0] * 品番数\n", "        \n", "        # 各在庫点について在庫量の最適調整量r^*を求める\n", "        for i in range(品番数):\n", "            if not inventory_distributions[i]:\n", "                continue\n", "            \n", "            inventory_counts = pd.Series(inventory_distributions[i]).value_counts(normalize=True).sort_index()\n", "            cumulative_distribution = inventory_counts.cumsum()\n", "            \n", "            # sum_{x <= r-1} f(x) <= h / (h + c)\n", "            prob_target = 在庫コスト単価 / (在庫コスト単価 + 出荷遅れコスト単価)\n", "            \n", "            best_r = 0\n", "            for r in cumulative_distribution.index:\n", "                prob_at_r_minus_1 = cumulative_distribution.get(r - 1, 0)\n", "                if prob_at_r_minus_1 <= prob_target:\n", "                    best_r = r\n", "                else:\n", "                    break\n", "            \n", "            adjustments[i] = s[i] - best_r\n", "            \n", "        print(f\"  今回の調整量: {adjustments}\")\n", "        \n", "        # すべての在庫点での初期在庫量をs:=s-r^*によって更新する\n", "        new_s = [s[i] - adjustments[i] for i in range(品番数)]\n", "        \n", "        # 終了条件のチェック\n", "        if all(abs(adj) < 1 for adj in adjustments):\n", "            print(\"--- アルゴリズムが収束しました。---\")\n", "            return s\n", "            \n", "        s = new_s\n", "        print(f\"  更新後の初期在庫量: {s}\")\n", "        \n", "    print(\"--- 最大反復回数に到達しました。---\")\n", "    return s\n", "\n", "\n", "def plot_results(best_individual, initial_inventory, save_path=None):\n", "    \"\"\"結果をプロットする関数\"\"\"\n", "    global 品番数, 期間, 品番リスト, 出荷数リスト, サイクルタイムリスト, 込め数リスト\n", "\n", "    print(\"\\n=== 結果のプロット ===\")\n", "    \n", "    total_inventory_per_period = []\n", "    total_production_time_per_period = []\n", "    total_setup_times_per_period = []\n", "    total_shipment_delay_per_period = []\n", "\n", "    inventory = initial_inventory[:]\n", "    max_daily_work_time = (8 + 2) * 60 * 2\n", "    daily_regular_time = 8 * 60 * 2\n", "    \n", "    for t in range(期間):\n", "        daily_inventory = 0\n", "        daily_production_time = 0\n", "        daily_setup_times = 0\n", "        daily_shipment_delay = 0\n", "\n", "        for i in range(品番数):\n", "            production = best_individual[t][i]\n", "\n", "            if production > 0:\n", "                daily_setup_times += 1\n", "                setup_time = 30\n", "            else:\n", "                setup_time = 0\n", "\n", "            production_time = (production / 込め数リスト[i]) * サイクルタイムリスト[i]\n", "            daily_production_time += production_time + setup_time\n", "\n", "            inventory[i] += production - 出荷数リスト[i]\n", "\n", "            if inventory[i] < 0:\n", "                daily_shipment_delay += abs(inventory[i])\n", "                inventory[i] = 0\n", "\n", "            daily_inventory += inventory[i]\n", "\n", "        total_inventory_per_period.append(daily_inventory)\n", "        total_production_time_per_period.append(daily_production_time)\n", "        total_setup_times_per_period.append(daily_setup_times)\n", "        total_shipment_delay_per_period.append(daily_shipment_delay)\n", "\n", "    fig, axes = plt.subplots(2, 2, figsize=(16, 12))\n", "    periods = list(range(1, 期間 + 1))\n", "\n", "    # 1. 各期間の総在庫量\n", "    axes[0, 0].bar(periods, total_inventory_per_period, color='skyblue', alpha=0.7)\n", "    axes[0, 0].set_title('各期間の総在庫量')\n", "    axes[0, 0].set_xlabel('期間')\n", "    axes[0, 0].set_ylabel('総在庫量 (個)')\n", "    axes[0, 0].grid(True, alpha=0.3)\n", "    if total_inventory_per_period:\n", "        axes[0, 0].set_ylim(0, max(total_inventory_per_period) * 1.2)\n", "\n", "    # 2. 各期間の総生産時間＋制限ライン\n", "    axes[0, 1].bar(periods, total_production_time_per_period, color='lightgreen', alpha=0.7)\n", "    axes[0, 1].axhline(y=max_daily_work_time, color='red', linestyle='--', alpha=0.8, label=f'上限 ({max_daily_work_time}分)')\n", "    axes[0, 1].axhline(y=daily_regular_time, color='green', linestyle='--', alpha=0.8, label=f'定時 ({daily_regular_time}分)')\n", "    axes[0, 1].set_title('各期間の総生産時間')\n", "    axes[0, 1].set_xlabel('期間')\n", "    axes[0, 1].set_ylabel('総稼働時間 (分)')\n", "    axes[0, 1].grid(True, alpha=0.3)\n", "    if total_production_time_per_period:\n", "        axes[0, 1].set_ylim(0, max(max_daily_work_time, max(total_production_time_per_period)) * 1.2)\n", "    axes[0, 1].legend()\n", "    \n", "    # 3. 各期間の総段替え回数\n", "    axes[1, 0].bar(periods, total_setup_times_per_period, color='salmon', alpha=0.7)\n", "    axes[1, 0].set_title('各期間の総段替え回数')\n", "    axes[1, 0].set_xlabel('期間')\n", "    axes[1, 0].set_ylabel('総段替え回数（回）')\n", "    axes[1, 0].grid(True, alpha=0.3)\n", "    if total_setup_times_per_period:\n", "        axes[1, 0].set_ylim(0, max(total_setup_times_per_period) * 1.2)\n", "\n", "    # 4. 各期間の総出荷遅れ量\n", "    axes[1, 1].bar(periods, total_shipment_delay_per_period, color='gold', alpha=0.7)\n", "    axes[1, 1].set_title('各期間の総出荷遅れ量')\n", "    axes[1, 1].set_xlabel('期間')\n", "    axes[1, 1].set_ylabel('総出荷遅れ量 (個)')\n", "    axes[1, 1].grid(True, alpha=0.3)\n", "    if total_shipment_delay_per_period:\n", "        axes[1, 1].set_ylim(0, max(total_shipment_delay_per_period) * 1.2)\n", "\n", "    plt.tight_layout()\n", "    \n", "    if save_path:\n", "        plt.savefig(save_path, dpi=300, bbox_inches='tight')\n", "        print(f\"プロットを保存: {save_path}\")\n", "    else:\n", "        plt.show()\n", "\n", "    time_violations = sum(1 for x in total_production_time_per_period if x > daily_regular_time + max_daily_work_time)\n", "    print(f\"時間制約違反: {time_violations} 期間\")\n", "\n", "    return total_inventory_per_period, total_production_time_per_period, total_setup_times_per_period, total_shipment_delay_per_period\n", "\n", "\n", "import os\n", "import time\n", "    \n", "def process_single_file(file_path):\n", "    \"\"\"単一のCSVファイルを処理する関数\"\"\"\n", "    global 品番数, 期間, 初期在庫量リスト\n", "    \n", "    print(f\"\\n=== Processing {file_path} ===\")\n", "    \n", "    # CSVファイルを読み込み\n", "    品番リスト, 出荷数リスト, 収容数リスト, サイクルタイムリスト, 込め数リスト, 初期在庫量リスト = read_csv(file_path)\n", "    品番数 = len(品番リスト)\n", "    期間 = 20\n", "    在庫コスト単価 = 180\n", "    出荷遅れコスト単価 = 500\n", "    \n", "    # 初期在庫量を調整\n", "    adjusted_initial_inventory = adjust_initial_inventory(初期在庫量リスト, 在庫コスト単価, 出荷遅れコスト単価, num_simulations=50, max_iterations=3)\n", "    初期在庫量リスト = adjusted_initial_inventory\n", "    \n", "    print(\"=== 多スタートローカルサーチ スケジューリング ===\")\n", "    \n", "    # 計算時間を測定\n", "    start_time = time.time()\n", "    num_starts = 30\n", "    best_solution, best_cost = multi_start_local_search(num_starts, 初期在庫量リスト)\n", "    calculation_time = time.time() - start_time\n", "    \n", "    if best_solution:\n", "        print(f\"\\n=== 最適化結果 ===\")\n", "        print(f\"最良個体の総コスト: {best_cost:.2f}\")\n", "        print(f\"計算時間: {calculation_time:.2f}秒\")\n", "        \n", "        # 結果をDataFrameに変換（期間×品番の形式）\n", "        result_df = pd.DataFrame(best_solution, \n", "                                index=[f\"期間_{t+1}\" for t in range(期間)],\n", "                                columns=[f\"品番_{i+1}\" for i in range(品番数)])\n", "        \n", "        # ファイル名から拡張子を除去\n", "        base_name = os.path.splitext(os.path.basename(file_path))[0]\n", "        \n", "        # 結果をCSVとして保存\n", "        result_csv_path = f\"result/multi_start_results_{base_name}.csv\"\n", "        result_df.to_csv(result_csv_path)\n", "        print(f\"結果をCSVファイルに保存: {result_csv_path}\")\n", "        \n", "        # プロットを作成して保存\n", "        plot_path = f\"result/multi_start_results_{base_name}.png\"\n", "        plot_results(best_solution, 初期在庫量リスト, save_path=plot_path)\n", "        \n", "        return best_cost, calculation_time, base_name\n", "    else:\n", "        print(\"\\n解が見つかりませんでした\")\n", "        return None, None, None\n", "\n", "def main():\n", "    \"\"\"メイン実行関数 - 全CSVファイルを処理\"\"\"\n", "    data_folder = \"data\"\n", "    \n", "    # dataフォルダ内のすべてのCSVファイルを取得\n", "    csv_files = [f for f in os.listdir(data_folder) if f.endswith('.csv')]\n", "    \n", "    if not csv_files:\n", "        print(\"dataフォルダにCSVファイルが見つかりません\")\n", "        return\n", "    \n", "    print(f\"見つかったCSVファイル: {csv_files}\")\n", "    \n", "    # 結果を格納するリスト\n", "    all_results = []\n", "    total_objective_value = 0\n", "    total_calculation_time = 0\n", "    \n", "    # 各CSVファイルを処理\n", "    for csv_file in csv_files:\n", "        file_path = os.path.join(data_folder, csv_file)\n", "        objective_value, calc_time, file_name = process_single_file(file_path)\n", "        \n", "        if objective_value is not None:\n", "            all_results.append({\n", "                'ファイル名': file_name,\n", "                '目的関数値': objective_value,\n", "                '計算時間': calc_time\n", "            })\n", "            total_objective_value += objective_value\n", "            total_calculation_time += calc_time\n", "    \n", "    # 集計結果をDataFrameに変換\n", "    summary_df = pd.DataFrame(all_results)\n", "    \n", "    # 合計行を追加\n", "    summary_row = pd.DataFrame({\n", "        'ファイル名': ['合計'],\n", "        '目的関数値': [total_objective_value],\n", "        '計算時間': [total_calculation_time]\n", "    })\n", "    summary_df = pd.concat([summary_df, summary_row], ignore_index=True)\n", "    \n", "    # 集計結果をCSVとして保存\n", "    summary_csv_path = \"result/multi_start_aggregate_results.csv\"\n", "    summary_df.to_csv(summary_csv_path, index=False)\n", "    print(f\"\\n集計結果をCSVファイルに保存: {summary_csv_path}\")\n", "    \n", "    # 結果の要約を表示\n", "    print(f\"\\n=== 全体の集計結果 ===\")\n", "    print(f\"処理したファイル数: {len(all_results)}\")\n", "    print(f\"総目的関数値: {total_objective_value:.2f}\")\n", "    print(f\"総計算時間: {total_calculation_time:.2f}秒\")\n", "    if len(all_results) > 0:\n", "        print(f\"平均目的関数値: {total_objective_value/len(all_results):.2f}\")\n", "        print(f\"平均計算時間: {total_calculation_time/len(all_results):.2f}秒\")\n", "        \n", "if __name__ == \"__main__\":\n", "    main()"]}, {"cell_type": "markdown", "id": "026f47a0", "metadata": {}, "source": []}, {"cell_type": "markdown", "id": "44e88872", "metadata": {}, "source": ["平均して何％くらい悪い  \n", "悪くなった例をもっと調べる  \n", "悪くなった原因がなんなのか  \n", "もっと探索か初期解生成を工夫する  \n", "同じ品番，別の生産期間と入れ替える，5日幅とかで見てスワップ？  \n", "需要量は変動しないものとしている  \n", "生産量の上限は，需要数＊マシン品番数まで  \n", "グループ分け（引き当て）の問題もある  \n", "もう少し多スタートに計算時間かけても良いかも  \n", "線形計画のほうで，期間を切り取る（t=20のうち5ずつ実行とか）  \n", "ローリング法？  \n", "MIPの方で最終在庫を考慮してないから在庫コスト低いんじゃないかな  "]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.7"}}, "nbformat": 4, "nbformat_minor": 5}