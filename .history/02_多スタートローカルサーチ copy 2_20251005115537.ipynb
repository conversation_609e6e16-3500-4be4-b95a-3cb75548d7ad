{"cells": [{"cell_type": "code", "execution_count": 1, "id": "6e757c78", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["見つかったCSVファイル: ['D36.csv']\n", "\n", "=== Processing data/D36.csv ===\n", "\n", "=== 初期在庫水準の調整アルゴリズム開始 ===\n", "--- 調整イテレーション 1 ---\n", "  今回の調整量: [163.63636363636363, 351.81818181818176, 30.909090909090907, 42.54545454545456, 500.909090909091, 1130.181818181818]\n", "  更新後の初期在庫量: [543.3636363636364, 866.1818181818182, 93.0909090909091, 155.45454545454544, 1156.090909090909, 2912.818181818182]\n", "--- 調整イテレーション 2 ---\n", "  今回の調整量: [163.63636363636363, 351.81818181818176, 30.909090909090907, 42.54545454545455, 500.909090909091, 1130.1818181818182]\n", "  更新後の初期在庫量: [379.72727272727275, 514.3636363636365, 62.18181818181819, 112.90909090909089, 655.181818181818, 1782.6363636363637]\n", "--- 調整イテレーション 3 ---\n", "  今回の調整量: [163.63636363636363, 351.8181818181818, 30.90909090909091, 42.54545454545455, 500.90909090909093, 1130.1818181818182]\n", "  更新後の初期在庫量: [216.09090909090912, 162.54545454545467, 31.272727272727277, 70.36363636363635, 154.27272727272708, 652.4545454545455]\n", "--- 最大反復回数に到達しました。---\n", "=== 多スタート焼きなまし法 スケジューリング ===\n", "--- Start 1/5 ---\n", "  New best solution found with total cost: 46957.66\n", "--- Start 2/5 ---\n", "--- Start 3/5 ---\n", "--- Start 4/5 ---\n", "--- Start 5/5 ---\n", "\n", "=== 最適化結果 ===\n", "最良個体の総コスト: 46957.66\n", "計算時間: 4.77秒\n"]}, {"data": {"image/png": "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*********************************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******************************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", "text/plain": ["<Figure size 1200x800 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", "=== 結果のプロット ===\n", "時間制約違反: 0 期間\n", "プロットを保存: result/sa_results_D36.png\n", "\n", "集計結果をCSVファイルに保存: result/simulated_annealing_aggregate_results.csv\n", "\n", "=== 全体の集計結果 ===\n", "処理したファイル数: 1\n", "総目的関数値: 46957.66\n", "総計算時間: 4.77秒\n", "平均目的関数値: 46957.66\n", "平均計算時間: 4.77秒\n"]}, {"data": {"image/png": "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", "text/plain": ["<Figure size 1600x1200 with 4 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["import random\n", "import numpy as np\n", "import pandas as pd\n", "import csv\n", "import matplotlib.pyplot as plt\n", "import japanize_matplotlib\n", "import copy\n", "import os\n", "import time\n", "\n", "# グローバル変数\n", "品番リスト = []\n", "出荷数リスト = []\n", "収容数リスト = []\n", "サイクルタイムリスト = []\n", "込め数リスト = []\n", "初期在庫量リスト = []\n", "品番数 = 0\n", "期間 = 0\n", "\n", "# コストとペナルティの係数\n", "在庫コスト単価 = 180\n", "残業コスト単価 = 66.7\n", "段替えコスト単価 = 400\n", "出荷遅れコスト単価 = 500\n", "\n", "定時 = 8 * 60 * 2\n", "最大残業時間 = 2 * 60 * 2\n", "段替え時間 = 30\n", "\n", "def read_csv(file_path):\n", "    \"\"\"CSVファイルを読み込む関数\"\"\"\n", "    global 品番リスト, 出荷数リスト, 容数リスト, サイクルタイムリスト, 込め数リスト, 初期在庫量リスト\n", "    \n", "    収容数辞書 = {}\n", "    try:\n", "        with open('収容数.csv', 'r', encoding='shift-jis') as capacity_file:\n", "            capacity_reader = csv.reader(capacity_file)\n", "            next(capacity_reader) # ヘッダーをスキップ\n", "            for row in capacity_reader:\n", "                if len(row) >= 2 and row[1].strip():\n", "                    品番 = row[0]\n", "                    収容数 = int(float(row[1]))\n", "                    収容数辞書[品番] = 収容数\n", "    except FileNotFoundError:\n", "        print(\"収容数.csvが見つかりません。デフォルト値（80）を使用します。\")\n", "    \n", "    with open(file_path, 'r', encoding='shift-jis') as file:\n", "        reader = csv.reader(file)\n", "        header = next(reader)\n", "        \n", "        品番リスト.clear()\n", "        出荷数リスト.clear()\n", "        収容数リスト.clear()\n", "        サイクルタイムリスト.clear()\n", "        込め数リスト.clear()\n", "        \n", "        期間数 = 22 # 期間数（日数）を定義\n", "        \n", "        rows = list(reader)\n", "        for row in rows:\n", "            if len(row) == 0:\n", "                continue\n", "            \n", "            total_quantity = int(row[header.index(\"個数\")])\n", "            \n", "            if total_quantity < 200:\n", "                continue\n", "            \n", "            daily_quantity = total_quantity / 期間数\n", "            \n", "            品番 = row[header.index(\"素材品番\")]\n", "            品番リスト.append(品番)\n", "            出荷数リスト.append(daily_quantity)\n", "            込め数リスト.append(int(float(row[header.index(\"込数\")])))\n", "            \n", "            cycle_time_per_unit = float(row[header.index(\"サイクルタイム\")]) / 60\n", "            サイクルタイムリスト.append(cycle_time_per_unit)\n", "            \n", "            収容数リスト.append(収容数辞書.get(品番, 80))\n", "            \n", "        初期在庫量リスト.clear()\n", "        for shipment in 出荷数リスト:\n", "            random_inventory = random.randint(int(shipment * 3), int(shipment * 5))\n", "            初期在庫量リスト.append(random_inventory)\n", "            \n", "    return 品番リスト, 出荷数リスト, 収容数リスト, サイクルタイムリスト, 込め数リスト, 初期在庫量リスト\n", "\n", "def evaluate(solution, current_initial_inventory):\n", "    \"\"\"総コスト（在庫、残業、段替え、出荷遅れ）を最小化する評価関数\"\"\"\n", "    \n", "    total_inventory_cost = 0\n", "    total_overtime_cost = 0\n", "    total_setup_cost = 0\n", "    total_shipment_delay_cost = 0\n", "    \n", "    inventory = current_initial_inventory[:]\n", "    inventory_history = [[] for _ in range(品番数)]\n", "    \n", "    for t in range(期間):\n", "        daily_time = 0\n", "        daily_setup_count = 0\n", "        \n", "        for i in range(品番数):\n", "            production = solution[t][i]\n", "            \n", "            if production > 0:\n", "                daily_setup_count += 1\n", "            \n", "            setup_time = 段替え時間 if production > 0 else 0\n", "            \n", "            production_time = (production / 込め数リスト[i]) * サイクルタイムリスト[i]\n", "            daily_time += production_time + setup_time\n", "            \n", "            inventory[i] += production - 出荷数リスト[i]\n", "            inventory_history[i].append(inventory[i])\n", "            \n", "            if inventory[i] < 0:\n", "                shortage_amount = abs(inventory[i])\n", "                total_shipment_delay_cost += 出荷遅れコスト単価 * shortage_amount\n", "            \n", "            if inventory[i] > 0:\n", "                total_inventory_cost += (inventory[i] / 収容数リスト[i]) * 在庫コスト単価\n", "        \n", "        total_setup_cost += 段替えコスト単価 * daily_setup_count\n", "        \n", "        if daily_time > 定時:\n", "            overtime = daily_time - 定時\n", "            total_overtime_cost += 残業コスト単価 * overtime\n", "        \n", "        if daily_time > 定時 + 最大残業時間:\n", "            work_time_penalty = (daily_time - (定時 + 最大残業時間)) * (残業コスト単価 * 100000)\n", "            total_overtime_cost += work_time_penalty\n", "            \n", "    total_cost = total_inventory_cost + total_overtime_cost + total_setup_cost + total_shipment_delay_cost\n", "    \n", "    return total_cost, inventory_history\n", "\n", "def generate_initial_solution(current_initial_inventory):\n", "    \"\"\"\n", "    需要予測と生産量平準化を考慮した初期解を生成する関数\n", "    \"\"\"\n", "    solution = []\n", "    temp_inventory = current_initial_inventory[:]\n", "    max_daily_work_time = (8 + 2) * 60 * 2\n", "    look_ahead_period = 3  # 3日先の需要まで見る\n", "\n", "    for t in range(期間):\n", "        daily_productions = [0] * 品番数\n", "        daily_time = 0\n", "        \n", "        # 生産優先順位を決定\n", "        # 在庫が少ない、かつ将来の需要が大きい品番を優先\n", "        priority_queue = []\n", "        for i in range(品番数):\n", "            # 将来の総需要を計算\n", "            future_demand = sum(出荷数リスト[i] for d in range(look_ahead_period) if t + d < 期間)\n", "            # 在庫と将来需要のバランスを評価\n", "            score = (temp_inventory[i] - future_demand) / 収容数リスト[i]\n", "            priority_queue.append((score, i))\n", "            \n", "        priority_queue.sort()\n", "\n", "        # 優先度の高い品番から生産量を決定\n", "        for score, i in priority_queue:\n", "            setup_time = 30 if daily_productions[i] == 0 else 0\n", "            remaining_time = max_daily_work_time - daily_time\n", "            \n", "            if remaining_time <= setup_time:\n", "                break\n", "            \n", "            cycle_time_per_unit = サイクルタイムリスト[i] / 込め数リスト[i]\n", "            if cycle_time_per_unit == 0: continue\n", "            \n", "            max_producible_by_time = int((remaining_time - setup_time) / cycle_time_per_unit)\n", "            \n", "            # 在庫が十分か確認\n", "            if temp_inventory[i] >= 出荷数リスト[i] * 2: # 2日分の在庫があれば今回は生産しない\n", "                continue\n", "            \n", "            # 目標在庫水準（例: 3日分）に達するように生産量を計算\n", "            target_inventory = 出荷数リスト[i] * 1\n", "            production = max(0, target_inventory - temp_inventory[i])\n", "            production = min(production, max_producible_by_time)\n", "            \n", "            if production > 0:\n", "                daily_productions[i] = production\n", "                daily_time += setup_time + (production / 込め数リスト[i]) * サイクルタイムリスト[i]\n", "        \n", "        solution.append(daily_productions)\n", "        \n", "        for i in range(品番数):\n", "            temp_inventory[i] += daily_productions[i] - 出荷数リスト[i]\n", "            \n", "    return solution\n", "\n", "def get_neighbors(current_solution):\n", "    \"\"\"より多様な近傍解を生成する関数\"\"\"\n", "    neighbors = []\n", "    num_neighbors = 30\n", "    \n", "    for _ in range(num_neighbors):\n", "        neighbor = copy.deepcopy(current_solution)\n", "        operation_type = random.choice(['swap', 'shift_time', 'adjust_amount', 'consolidate'])\n", "        \n", "        if operation_type == 'swap':\n", "            t1, t2 = random.randint(0, 期間 - 1), random.randint(0, 期間 - 1)\n", "            i1, i2 = random.randint(0, 品番数 - 1), random.randint(0, 品番数 - 1)\n", "            if (t1, i1) != (t2, i2):\n", "                neighbor[t1][i1], neighbor[t2][i2] = neighbor[t2][i2], neighbor[t1][i1]\n", "\n", "        elif operation_type == 'shift_time':\n", "            t_from = random.randint(0, 期間 - 1)\n", "            t_to = random.randint(0, 期間 - 1)\n", "            i = random.randint(0, 品番数 - 1)\n", "            if t_from != t_to:\n", "                production_amount = neighbor[t_from][i]\n", "                if production_amount > 0:\n", "                    neighbor[t_from][i] = 0\n", "                    neighbor[t_to][i] += production_amount\n", "\n", "        elif operation_type == 'adjust_amount':\n", "            t = random.randint(0, 期間 - 1)\n", "            i = random.randint(0, 品番数 - 1)\n", "            change = random.randint(-100, 100)\n", "            neighbor[t][i] = max(0, neighbor[t][i] + change)\n", "\n", "        elif operation_type == 'consolidate':\n", "            i = random.randint(0, 品番数 - 1)\n", "            t_target = random.randint(0, 期間 - 1)\n", "            total_production_for_part = sum(neighbor[t][i] for t in range(期間))\n", "            for t in range(期間):\n", "                neighbor[t][i] = 0\n", "            neighbor[t_target][i] = total_production_for_part\n", "            \n", "        neighbors.append(neighbor)\n", "    return neighbors\n", "\n", "def simulated_annealing(initial_solution, current_initial_inventory, history_list):\n", "    \"\"\"\n", "    焼きなまし法を実行する関数\n", "    \"\"\"\n", "    current_solution = initial_solution\n", "    current_cost, _ = evaluate(current_solution, current_initial_inventory)\n", "    best_solution = current_solution\n", "    best_cost = current_cost\n", "    \n", "    # 焼きなまし法のパラメータ\n", "    T = 100000.0  # 初期温度\n", "    alpha = 0.995 # 冷却率\n", "    \n", "    iteration_limit = 1000 # 繰り返し回数上限\n", "\n", "    for i in range(iteration_limit):\n", "        if T < 1e-6: # 温度が十分に下がったら終了\n", "            break\n", "\n", "        # 近傍解を1つ生成（より効率的な近傍解生成に切り替え）\n", "        neighbor = get_neighbors(current_solution)[0]\n", "        neighbor_cost, _ = evaluate(neighbor, current_initial_inventory)\n", "        \n", "        delta_cost = neighbor_cost - current_cost\n", "\n", "        # 改善した場合は常に受け入れる\n", "        if delta_cost < 0:\n", "            current_solution = neighbor\n", "            current_cost = neighbor_cost\n", "            if current_cost < best_cost:\n", "                best_solution = current_solution\n", "                best_cost = current_cost\n", "        else:\n", "            # 悪化した場合は、確率で受け入れる\n", "            acceptance_probability = np.exp(-delta_cost / T)\n", "            if random.random() < acceptance_probability:\n", "                current_solution = neighbor\n", "                current_cost = neighbor_cost\n", "\n", "        # 温度を下げる\n", "        T *= alpha\n", "\n", "        # 履歴を記録\n", "        history_list.append(current_cost)\n", "            \n", "    return best_solution, best_cost\n", "\n", "def multi_start_sa(num_starts, current_initial_inventory):\n", "    \"\"\"多スタート焼きなまし法を実行する関数\"\"\"\n", "    best_solution_overall = None\n", "    best_cost_overall = float('inf')\n", "    search_histories = []\n", "\n", "    for i in range(num_starts):\n", "        print(f\"--- Start {i+1}/{num_starts} ---\")\n", "        initial_solution = generate_initial_solution(current_initial_inventory)\n", "        current_history = []\n", "        local_optimal_solution, local_optimal_cost = simulated_annealing(initial_solution, current_initial_inventory, current_history)\n", "        search_histories.append({'start_num': i + 1, 'history': current_history, 'final_cost': local_optimal_cost})\n", "        \n", "        if local_optimal_cost < best_cost_overall:\n", "            best_cost_overall = local_optimal_cost\n", "            best_solution_overall = local_optimal_solution\n", "            print(f\"  New best solution found with total cost: {best_cost_overall:.2f}\")\n", "            \n", "    return best_solution_overall, best_cost_overall, search_histories\n", "\n", "def simulate_production_schedule_simple(initial_inventory, 期間=20):\n", "    \"\"\"生産スケジュールをシミュレートする関数（簡易版）\"\"\"\n", "    global 品番数\n", "    品番数 = len(initial_inventory)\n", "    inventory = initial_inventory[:]\n", "    inventory_history = [[] for _ in range(品番数)]\n", "    \n", "    daily_regular_time = 8 * 60 * 2\n", "    max_daily_overtime = 2 * 60 * 2\n", "    max_daily_work_time = daily_regular_time + max_daily_overtime\n", "    \n", "    for t in range(期間):\n", "        daily_production_time = 0\n", "        daily_setup_count = 0\n", "        \n", "        for i in range(品番数):\n", "            demand = 出荷数リスト[i]\n", "            inventory[i] -= demand\n", "            \n", "            if inventory[i] < 0:\n", "                shortage = abs(inventory[i])\n", "                production = shortage\n", "                \n", "                if production > 0:\n", "                    daily_setup_count += 1\n", "                    setup_time = 30\n", "                    production_time = (production / 込め数リスト[i]) * サイクルタイムリスト[i]\n", "                    daily_production_time += production_time + setup_time\n", "                \n", "                inventory[i] += production\n", "            \n", "            inventory_history[i].append(max(0, inventory[i]))\n", "        \n", "        if daily_production_time > max_daily_work_time:\n", "            reduction_factor = max_daily_work_time / daily_production_time\n", "            for i in range(品番数):\n", "                if inventory[i] > 0:\n", "                    current_production = max(0, 出荷数リスト[i] - (initial_inventory[i] - inventory[i]))\n", "                    reduced_production = current_production * reduction_factor\n", "                    inventory[i] = initial_inventory[i] + reduced_production - 出荷数リスト[i] * (t + 1)\n", "                    inventory[i] = max(0, inventory[i])\n", "                    inventory_history[i][-1] = inventory[i]\n", "    return inventory_history\n", "\n", "def adjust_initial_inventory(初期在庫量リスト, 在庫コスト単価, 出荷遅れコスト単価, num_simulations=50, max_iterations=3):\n", "    \"\"\"初期在庫量を最適化する関数（簡易シミュレーション使用版）\"\"\"\n", "    \n", "    品番数 = len(初期在庫量リスト)\n", "    s = 初期在庫量リスト[:]\n", "    \n", "    # h / (h+c) - optimize_initial_inventoryと同じ計算方法\n", "    prob_target = 在庫コスト単価 / (在庫コスト単価 + 出荷遅れコスト単価)\n", "    \n", "    print(f\"\\n=== 初期在庫水準の調整アルゴリズム開始 ===\")\n", "    \n", "    for iteration in range(max_iterations):\n", "        print(f\"--- 調整イテレーション {iteration+1} ---\")\n", "        inventory_distributions = [[] for _ in range(品番数)]\n", "        for _ in range(num_simulations):\n", "            inventory_history = simulate_production_schedule(s)\n", "            for i in range(品番数):\n", "                inventory_distributions[i].extend(inventory_history[i])\n", "        \n", "        adjustments = [0] * 品番数\n", "        \n", "        for i in range(品番数):\n", "            if not inventory_distributions[i]:\n", "                continue\n", "            \n", "            inventory_counts = pd.Series(inventory_distributions[i]).value_counts(normalize=True).sort_index()\n", "            cumulative_distribution = inventory_counts.cumsum()\n", "            \n", "            # sum_{x <= r-1} f(x) <= h / (h + c) - optimize_initial_inventoryと同じロジック\n", "            best_r = 0\n", "            for r in cumulative_distribution.index:\n", "                prob_at_r_minus_1 = cumulative_distribution.get(r - 1, 0)\n", "                if prob_at_r_minus_1 <= prob_target:\n", "                    best_r = r\n", "                else:\n", "                    break\n", "            \n", "            adjustments[i] = s[i] - best_r\n", "            \n", "        print(f\"  今回の調整量: {adjustments}\")\n", "        \n", "        # すべての在庫点での初期在庫量をs:=s-r^*によって更新する\n", "        new_s = [s[i] - adjustments[i] for i in range(品番数)]\n", "        \n", "        # 終了条件のチェック - optimize_initial_inventoryと同じ条件\n", "        if all(abs(adj) < 1 for adj in adjustments):\n", "            print(\"--- アルゴリズムが収束しました。---\")\n", "            return s\n", "            \n", "        s = new_s\n", "        print(f\"  更新後の初期在庫量: {s}\")\n", "        \n", "    print(\"--- 最大反復回数に到達しました。---\")\n", "    return s\n", "\n", "def plot_results(best_individual, initial_inventory, save_path=None):\n", "    \"\"\"結果をプロットする関数\"\"\"\n", "    global 品番数, 期間, 出荷数リスト, サイクルタイムリスト, 込め数リスト\n", "\n", "    print(\"\\n=== 結果のプロット ===\")\n", "    total_inventory_per_period = []\n", "    total_production_time_per_period = []\n", "    total_setup_times_per_period = []\n", "    total_shipment_delay_per_period = []\n", "    inventory = initial_inventory[:]\n", "    max_daily_work_time = (8 + 2) * 60 * 2\n", "    daily_regular_time = 8 * 60 * 2\n", "    \n", "    for t in range(期間):\n", "        daily_inventory = 0\n", "        daily_production_time = 0\n", "        daily_setup_times = 0\n", "        daily_shipment_delay = 0\n", "\n", "        for i in range(品番数):\n", "            production = best_individual[t][i]\n", "\n", "            if production > 0:\n", "                daily_setup_times += 1\n", "                setup_time = 30\n", "            else:\n", "                setup_time = 0\n", "\n", "            production_time = (production / 込め数リスト[i]) * サイクルタイムリスト[i]\n", "            daily_production_time += production_time + setup_time\n", "            inventory[i] += production - 出荷数リスト[i]\n", "\n", "            if inventory[i] < 0:\n", "                daily_shipment_delay += abs(inventory[i])\n", "                inventory[i] = 0\n", "            daily_inventory += inventory[i]\n", "\n", "        total_inventory_per_period.append(daily_inventory)\n", "        total_production_time_per_period.append(daily_production_time)\n", "        total_setup_times_per_period.append(daily_setup_times)\n", "        total_shipment_delay_per_period.append(daily_shipment_delay)\n", "\n", "    fig, axes = plt.subplots(2, 2, figsize=(16, 12))\n", "    periods = list(range(1, 期間 + 1))\n", "\n", "    axes[0, 0].bar(periods, total_inventory_per_period, color='skyblue', alpha=0.7)\n", "    axes[0, 0].set_title('各期間の総在庫量')\n", "    axes[0, 0].set_xlabel('期間')\n", "    axes[0, 0].set_ylabel('総在庫量 (個)')\n", "    axes[0, 0].grid(True, alpha=0.3)\n", "\n", "    axes[0, 1].bar(periods, total_production_time_per_period, color='lightgreen', alpha=0.7)\n", "    axes[0, 1].axhline(y=max_daily_work_time, color='red', linestyle='--', alpha=0.8, label=f'上限 ({max_daily_work_time}分)')\n", "    axes[0, 1].axhline(y=daily_regular_time, color='green', linestyle='--', alpha=0.8, label=f'定時 ({daily_regular_time}分)')\n", "    axes[0, 1].set_title('各期間の総生産時間')\n", "    axes[0, 1].set_xlabel('期間')\n", "    axes[0, 1].set_ylabel('総稼働時間 (分)')\n", "    axes[0, 1].grid(True, alpha=0.3)\n", "    axes[0, 1].legend()\n", "    \n", "    axes[1, 0].bar(periods, total_setup_times_per_period, color='salmon', alpha=0.7)\n", "    axes[1, 0].set_title('各期間の総段替え回数')\n", "    axes[1, 0].set_xlabel('期間')\n", "    axes[1, 0].set_ylabel('総段替え回数（回）')\n", "    axes[1, 0].grid(True, alpha=0.3)\n", "\n", "    axes[1, 1].bar(periods, total_shipment_delay_per_period, color='gold', alpha=0.7)\n", "    axes[1, 1].set_title('各期間の総出荷遅れ量')\n", "    axes[1, 1].set_xlabel('期間')\n", "    axes[1, 1].set_ylabel('総出荷遅れ量 (個)')\n", "    axes[1, 1].grid(True, alpha=0.3)\n", "    \n", "    # 時間制約違反のチェック\n", "    time_violations = sum(1 for x in total_production_time_per_period if x > max_daily_work_time)\n", "    print(f\"時間制約違反: {time_violations} 期間\")\n", "\n", "    plt.tight_layout()\n", "    if save_path:\n", "        plt.savefig(save_path, dpi=300, bbox_inches='tight')\n", "        print(f\"プロットを保存: {save_path}\")\n", "    else:\n", "        plt.show()\n", "\n", "def plot_search_history(search_histories, save_path=None):\n", "    \"\"\"各スタートの探索過程（コストの推移）をプロットする関数\"\"\"\n", "    plt.figure(figsize=(12, 8))\n", "    for history_data in search_histories:\n", "        history = history_data['history']\n", "        start_num = history_data['start_num']\n", "        x_axis = range(len(history))\n", "        plt.plot(x_axis, history, label=f'スタート {start_num}')\n", "    plt.title('各スタートの焼きなまし法探索履歴')\n", "    plt.xlabel('ステップ数')\n", "    plt.ylabel('総コスト')\n", "    plt.grid(True, linestyle='--', alpha=0.6)\n", "    plt.legend(bbox_to_anchor=(1.05, 1), loc='upper left')\n", "    plt.tight_layout(rect=[0, 0, 0.85, 1])\n", "    if save_path:\n", "        plt.savefig(save_path, dpi=300, bbox_inches='tight')\n", "        print(f\"探索履歴プロットを保存: {save_path}\")\n", "    else:\n", "        plt.show()\n", "    plt.close()\n", "\n", "def process_single_file(file_path):\n", "    \"\"\"単一のCSVファイルを処理する関数\"\"\"\n", "    global 品番数, 期間\n", "    print(f\"\\n=== Processing {file_path} ===\")\n", "    \n", "    品番リスト, 出荷数リスト, 収容数リスト, サイクルタイムリスト, 込め数リスト, 初期在庫量リスト = read_csv(file_path)\n", "    品番数 = len(品番リスト)\n", "    期間 = 20\n", "    \n", "    adjusted_initial_inventory = adjust_initial_inventory(初期在庫量リスト, 在庫コスト単価, 出荷遅れコスト単価, num_simulations=50, max_iterations=3)\n", "    初期在庫量リスト[:] = adjusted_initial_inventory\n", "    \n", "    print(\"=== 多スタート焼きなまし法 スケジューリング ===\")\n", "    start_time = time.time()\n", "    num_starts = 5 # 試行回数を増やして探索を強化\n", "    best_solution, best_cost, search_histories = multi_start_sa(num_starts, 初期在庫量リスト)\n", "    calculation_time = time.time() - start_time\n", "    \n", "    if best_solution:\n", "        print(f\"\\n=== 最適化結果 ===\")\n", "        print(f\"最良個体の総コスト: {best_cost:.2f}\")\n", "        print(f\"計算時間: {calculation_time:.2f}秒\")\n", "        \n", "        base_name = os.path.splitext(os.path.basename(file_path))[0]\n", "        result_csv_path = f\"result/sa_results_{base_name}.csv\"\n", "        plot_path = f\"result/sa_results_{base_name}.png\"\n", "        \n", "        plot_search_history(search_histories)\n", "        plot_results(best_solution, 初期在庫量リスト, plot_path)\n", "        \n", "        return best_cost, calculation_time, base_name\n", "    else:\n", "        print(\"\\n解が見つかりませんでした\")\n", "        return None, None, None\n", "\n", "def main():\n", "    \"\"\"メイン実行関数 - 全CSVファイルを処理\"\"\"\n", "    data_folder = \"data\"\n", "    \n", "    if not os.path.exists(data_folder):\n", "        print(f\"'{data_folder}' フォルダが見つかりません。データファイルを配置してください。\")\n", "        return\n", "\n", "    csv_files = [f for f in os.listdir(data_folder) if f.endswith('.csv')]\n", "    \n", "    if not csv_files:\n", "        print(f\"'{data_folder}' フォルダにCSVファイルが見つかりません\")\n", "        return\n", "    \n", "    if not os.path.exists('result'):\n", "        os.makedirs('result')\n", "    \n", "    print(f\"見つかったCSVファイル: {csv_files}\")\n", "    \n", "    all_results = []\n", "    total_objective_value = 0\n", "    total_calculation_time = 0\n", "    \n", "    for csv_file in csv_files:\n", "        file_path = os.path.join(data_folder, csv_file)\n", "        objective_value, calc_time, file_name = process_single_file(file_path)\n", "        \n", "        if objective_value is not None:\n", "            all_results.append({\n", "                'ファイル名': file_name,\n", "                '目的関数値': objective_value,\n", "                '計算時間': calc_time\n", "            })\n", "            total_objective_value += objective_value\n", "            total_calculation_time += calc_time\n", "    \n", "    summary_df = pd.DataFrame(all_results)\n", "    \n", "    summary_row = pd.DataFrame({\n", "        'ファイル名': ['合計'],\n", "        '目的関数値': [total_objective_value],\n", "        '計算時間': [total_calculation_time]\n", "    })\n", "    summary_df = pd.concat([summary_df, summary_row], ignore_index=True)\n", "    \n", "    summary_csv_path = \"result/simulated_annealing_aggregate_results.csv\"\n", "    summary_df.to_csv(summary_csv_path, index=False, encoding='shift-jis')\n", "    print(f\"\\n集計結果をCSVファイルに保存: {summary_csv_path}\")\n", "    \n", "    print(f\"\\n=== 全体の集計結果 ===\")\n", "    print(f\"処理したファイル数: {len(all_results)}\")\n", "    print(f\"総目的関数値: {total_objective_value:.2f}\")\n", "    print(f\"総計算時間: {total_calculation_time:.2f}秒\")\n", "    if len(all_results) > 0:\n", "        print(f\"平均目的関数値: {total_objective_value/len(all_results):.2f}\")\n", "        print(f\"平均計算時間: {total_calculation_time/len(all_results):.2f}秒\")\n", "        \n", "if __name__ == \"__main__\":\n", "    main()"]}, {"cell_type": "markdown", "id": "026f47a0", "metadata": {}, "source": []}, {"cell_type": "markdown", "id": "44e88872", "metadata": {}, "source": ["平均して何％くらい悪い  \n", "悪くなった例をもっと調べる  \n", "悪くなった原因がなんなのか  \n", "もっと探索か初期解生成を工夫する  \n", "同じ品番，別の生産期間と入れ替える，5日幅とかで見てスワップ？  \n", "需要量は変動しないものとしている  \n", "生産量の上限は，需要数＊マシン品番数まで  \n", "グループ分け（引き当て）の問題もある  \n", "もう少し多スタートに計算時間かけても良いかも  \n", "線形計画のほうで，期間を切り取る（t=20のうち5ずつ実行とか）  \n", "ローリング法？  \n", "MIPの方で最終在庫を考慮してないから在庫コスト低いんじゃないかな  "]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.7"}}, "nbformat": 4, "nbformat_minor": 5}