{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["=== 最適化手法比較サマリー ===\n"]}], "source": ["import pandas as pd\n", "import matplotlib.pyplot as plt\n", "import japanize_matplotlib\n", "import numpy as np\n", "import os\n", "\n", "# 結果ファイルのパス\n", "mip_results_path = 'result/MIP_aggregate_results.csv'\n", "multistart_results_path = 'result/multi_start_aggregate_results.csv'\n", "\n", "print(\"=== 最適化手法比較サマリー ===\")"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["MIP結果ファイルの読み込みエラー: 'utf-8' codec can't decode byte 0x83 in position 0: invalid start byte\n", "MIP結果ファイルを読み込みました（Shift_JIS）\n", "  ファイル名         目的関数値       計算時間\n", "0   D36   48240.00000   0.531276\n", "1   D40  204149.79397  16.631087\n", "2    合計  252389.79397  17.162363\n"]}], "source": ["# CSVファイルを読み込み\n", "try:\n", "    mip_df = pd.read_csv(mip_results_path, encoding='utf-8')\n", "    print(\"MIP結果ファイルを読み込みました\")\n", "    print(mip_df)\n", "except Exception as e:\n", "    print(f\"MIP結果ファイルの読み込みエラー: {e}\")\n", "    # エンコーディングを変更して再試行\n", "    try:\n", "        mip_df = pd.read_csv(mip_results_path, encoding='shift_jis')\n", "        print(\"MIP結果ファイルを読み込みました（Shift_JIS）\")\n", "        print(mip_df)\n", "    except Exception as e2:\n", "        print(f\"MIP結果ファイルの読み込みに失敗: {e2}\")\n", "        mip_df = None"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "多スタートローカルサーチ結果ファイルを読み込みました\n", "  ファイル名         目的関数値      計算時間\n", "0   D36  51533.181818  0.081504\n", "1    合計  51533.181818  0.081504\n"]}], "source": ["try:\n", "    multistart_df = pd.read_csv(multistart_results_path, encoding='utf-8')\n", "    print(\"\\n多スタートローカルサーチ結果ファイルを読み込みました\")\n", "    print(multistart_df)\n", "except Exception as e:\n", "    print(f\"多スタートローカルサーチ結果ファイルの読み込みエラー: {e}\")\n", "    multistart_df = None"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "=== 合計値比較 ===\n", "MIP - 目的関数値: 252389.79, 計算時間: 17.1624秒\n", "多スタートローカルサーチ - 目的関数値: 51533.18, 計算時間: 0.0815秒\n", "\n", "=== 比較結果 ===\n", "目的関数値の差: -200856.61\n", "目的関数値の変化率: -79.58% (正の値は多スタートが悪い)\n", "計算時間の比率: 0.00 (多スタート/MIP)\n"]}], "source": ["# データの前処理と比較\n", "if mip_df is not None and multistart_df is not None:\n", "    # 合計行を抽出\n", "    mip_total = mip_df[mip_df.iloc[:, 0].str.contains('合計|総計|Total', na=False)]\n", "    multistart_total = multistart_df[multistart_df.iloc[:, 0].str.contains('合計|総計|Total', na=False)]\n", "    \n", "    if len(mip_total) > 0 and len(multistart_total) > 0:\n", "        # 合計値を取得\n", "        mip_objective = float(mip_total.iloc[0, 1])  # 目的関数値\n", "        mip_time = float(mip_total.iloc[0, 2])       # 計算時間\n", "        \n", "        multistart_objective = float(multistart_total.iloc[0, 1])  # 目的関数値\n", "        multistart_time = float(multistart_total.iloc[0, 2])       # 計算時間\n", "        \n", "        print(f\"\\n=== 合計値比較 ===\")\n", "        print(f\"MIP - 目的関数値: {mip_objective:.2f}, 計算時間: {mip_time:.4f}秒\")\n", "        print(f\"多スタートローカルサーチ - 目的関数値: {multistart_objective:.2f}, 計算時間: {multistart_time:.4f}秒\")\n", "        \n", "        # 改善率の計算\n", "        objective_improvement = ((multistart_objective - mip_objective) / mip_objective) * 100\n", "        time_ratio = multistart_time / mip_time\n", "        \n", "        print(f\"\\n=== 比較結果 ===\")\n", "        print(f\"目的関数値の差: {multistart_objective - mip_objective:.2f}\")\n", "        print(f\"目的関数値の変化率: {objective_improvement:.2f}% (正の値は多スタートが悪い)\")\n", "        print(f\"計算時間の比率: {time_ratio:.2f} (多スタート/MIP)\")\n", "    else:\n", "        print(\"合計行が見つかりませんでした\")\n", "        mip_objective = mip_time = multistart_objective = multistart_time = None\n", "else:\n", "    print(\"データの読み込みに失敗したため、比較できません\")\n", "    mip_objective = mip_time = multistart_objective = multistart_time = None"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "=== 個別データセット比較 ===\n", "MIP個別結果:\n", "  ファイル名         目的関数値       計算時間\n", "0   D36   48240.00000   0.531276\n", "1   D40  204149.79397  16.631087\n", "\n", "多スタートローカルサーチ個別結果:\n", "  ファイル名         目的関数値      計算時間\n", "0   D36  51533.181818  0.081504\n"]}], "source": ["# 個別データセットの比較も行う\n", "if mip_df is not None and multistart_df is not None:\n", "    # 合計行以外のデータを取得\n", "    mip_individual = mip_df[~mip_df.iloc[:, 0].str.contains('合計|総計|Total', na=False)]\n", "    multistart_individual = multistart_df[~multistart_df.iloc[:, 0].str.contains('合計|総計|Total', na=False)]\n", "    \n", "    print(f\"\\n=== 個別データセット比較 ===\")\n", "    print(f\"MIP個別結果:\")\n", "    print(mip_individual)\n", "    print(f\"\\n多スタートローカルサーチ個別結果:\")\n", "    print(multistart_individual)"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "比較プロットを保存しました: result/optimization_comparison.png\n"]}, {"data": {"image/png": "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", "text/plain": ["<Figure size 1500x600 with 2 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", "=== 詳細比較テーブル ===\n", "          手法     目的関数値 計算時間(秒)    相対性能  時間効率\n", "         MIP 252389.79 17.1624      基準    基準\n", "多スタートローカルサーチ  51533.18  0.0815 -79.58% 0.00倍\n", "\n", "比較テーブルを保存しました: result/method_comparison_summary.csv\n"]}], "source": ["# 比較プロットの作成\n", "if mip_objective is not None and multistart_objective is not None:\n", "    # データの準備\n", "    methods = ['MIP', '多スタートローカルサーチ']\n", "    objectives = [mip_objective, multistart_objective]\n", "    times = [mip_time, multistart_time]\n", "    \n", "    # プロットの作成\n", "    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 6))\n", "    \n", "    # 目的関数値の比較\n", "    bars1 = ax1.bar(methods, objectives, color=['skyblue', 'lightcoral'], alpha=0.7)\n", "    ax1.set_title('目的関数値の比較', fontsize=14, fontweight='bold')\n", "    ax1.set_ylabel('目的関数値')\n", "    ax1.grid(True, alpha=0.3)\n", "    \n", "    # 値をバーの上に表示\n", "    for bar, value in zip(bars1, objectives):\n", "        height = bar.get_height()\n", "        ax1.text(bar.get_x() + bar.get_width()/2., height + height*0.01,\n", "                f'{value:.0f}', ha='center', va='bottom', fontweight='bold')\n", "    \n", "    # 計算時間の比較\n", "    bars2 = ax2.bar(methods, times, color=['lightgreen', 'orange'], alpha=0.7)\n", "    ax2.set_title('計算時間の比較', fontsize=14, fontweight='bold')\n", "    ax2.set_ylabel('計算時間 (秒)')\n", "    ax2.grid(True, alpha=0.3)\n", "    \n", "    # 値をバーの上に表示\n", "    for bar, value in zip(bars2, times):\n", "        height = bar.get_height()\n", "        ax2.text(bar.get_x() + bar.get_width()/2., height + height*0.01,\n", "                f'{value:.4f}', ha='center', va='bottom', fontweight='bold')\n", "    \n", "    plt.tight_layout()\n", "    \n", "    # プロットを保存\n", "    output_path = 'result/optimization_comparison.png'\n", "    plt.savefig(output_path, dpi=300, bbox_inches='tight')\n", "    print(f\"\\n比較プロットを保存しました: {output_path}\")\n", "    \n", "    plt.show()\n", "    \n", "    # 詳細比較テーブルの作成\n", "    comparison_data = {\n", "        '手法': ['MIP', '多スタートローカルサーチ'],\n", "        '目的関数値': [f'{mip_objective:.2f}', f'{multistart_objective:.2f}'],\n", "        '計算時間(秒)': [f'{mip_time:.4f}', f'{multistart_time:.4f}'],\n", "        '相対性能': ['基準', f'{objective_improvement:+.2f}%'],\n", "        '時間効率': ['基準', f'{time_ratio:.2f}倍']\n", "    }\n", "    \n", "    comparison_df = pd.DataFrame(comparison_data)\n", "    print(f\"\\n=== 詳細比較テーブル ===\")\n", "    print(comparison_df.to_string(index=False))\n", "    \n", "    # 比較テーブルをCSVとして保存\n", "    comparison_csv_path = 'result/method_comparison_summary.csv'\n", "    comparison_df.to_csv(comparison_csv_path, index=False, encoding='utf-8')\n", "    print(f\"\\n比較テーブルを保存しました: {comparison_csv_path}\")\n", "    \n", "else:\n", "    print(\"データが不足しているため、プロットを作成できません\")"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.7"}}, "nbformat": 4, "nbformat_minor": 4}