#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
多スタート焼きなまし法による生産スケジューリング実行スクリプト
"""

import os
import glob
import time
import pandas as pd
from process import (
    read_csv, adjust_initial_inventory, multi_start_simulated_annealing,
    plot_result, save_results_to_csv, calculate_summary_statistics, plot_aggregate_results
)
# グローバル変数をインポート
import process

# ===== パラメータ設定 =====
NUM_STARTS = 5          # スタート数
MAX_ITERATIONS = 1000   # 最大反復回数
INITIAL_TEMP = 1000     # 初期温度
COOLING_RATE = 0.95     # 冷却率
DATA_FOLDER = 'data'    # データフォルダ

def run_multistart_for_single_file(csv_file):
    """単一ファイルに対して多スタート焼きなまし法を実行する関数"""
    file_name = os.path.basename(csv_file).replace('.csv', '')
    print(f"\n=== Processing {csv_file} ===")

    try:
        # データを読み込み
        read_csv(csv_file)

        # 初期在庫調整
        print("\n=== 初期在庫水準の調整アルゴリズム開始 ===")
        adjusted_inventory = adjust_initial_inventory(process.初期在庫量リスト)

        # 多スタート焼きなまし法実行
        print(f"\n=== 多スタート焼きなまし法 スケジューリング ===")
        print(f"スタート数: {NUM_STARTS}")
        print(f"最大反復回数: {MAX_ITERATIONS}")
        print(f"初期温度: {INITIAL_TEMP}")
        print(f"冷却率: {COOLING_RATE}")

        start_time = time.time()
        production_schedule, total_cost = multi_start_simulated_annealing(
            adjusted_inventory,
            num_starts=NUM_STARTS,
            max_iterations=MAX_ITERATIONS,
            initial_temp=INITIAL_TEMP,
            cooling_rate=COOLING_RATE
        )
        sa_time = time.time() - start_time

        print(f"\n=== 最適化結果 ===")
        print(f"最良個体の総コスト: {total_cost:.2f}")
        print(f"計算時間: {sa_time:.2f}秒")

        # 結果データを作成
        result_data = {
            'データファイル': file_name,
            '手法': 'SA',
            '総コスト': total_cost,
            '計算時間': sa_time,
            '品番数': len(process.品番リスト),
            '調整前初期在庫合計': sum(process.初期在庫量リスト),
            '調整後初期在庫合計': sum(adjusted_inventory),
            '在庫削減量': sum(process.初期在庫量リスト) - sum(adjusted_inventory),
            '在庫削減率(%)': ((sum(process.初期在庫量リスト) - sum(adjusted_inventory)) / sum(process.初期在庫量リスト)) * 100 if sum(process.初期在庫量リスト) > 0 else 0,
            'スタート数': NUM_STARTS,
            '最大反復回数': MAX_ITERATIONS,
            '初期温度': INITIAL_TEMP,
            '冷却率': COOLING_RATE
        }

        # 結果をCSVに保存
        csv_path = save_results_to_csv([result_data], f'SA_results_{file_name}.csv')

        # 結果をプロット
        violation_count = plot_result(production_schedule, adjusted_inventory, file_name, "SA")
        result_data['時間制約違反期間数'] = violation_count

        return result_data

    except Exception as e:
        print(f"エラーが発生しました ({csv_file}): {e}")
        return None

def run_multistart_batch():
    """データフォルダ内の全CSVファイルに対して多スタート焼きなまし法を実行する関数"""
    csv_files = glob.glob(os.path.join(DATA_FOLDER, '*.csv'))

    if not csv_files:
        print(f"データフォルダ '{DATA_FOLDER}' にCSVファイルが見つかりません。")
        return None

    print(f"見つかったCSVファイル: {[os.path.basename(f) for f in csv_files]}")
    print(f"パラメータ設定:")
    print(f"  スタート数: {NUM_STARTS}")
    print(f"  最大反復回数: {MAX_ITERATIONS}")
    print(f"  初期温度: {INITIAL_TEMP}")
    print(f"  冷却率: {COOLING_RATE}")

    all_results = []
    total_start_time = time.time()

    for csv_file in csv_files:
        result = run_multistart_for_single_file(csv_file)
        if result:
            all_results.append(result)

    total_time = time.time() - total_start_time

    if all_results:
        print(f"\n=== バッチ処理完了 ===")
        print(f"総処理時間: {total_time:.2f}秒")
        print(f"処理ファイル数: {len(all_results)}")

        # 集約結果をCSVに保存
        aggregate_csv = save_results_to_csv(all_results, 'SA_aggregate_results.csv')

        # 集約結果をプロット
        plot_filename = plot_aggregate_results(all_results, "SA")

        # 統計情報を計算
        summary_df, stats_csv = calculate_summary_statistics(all_results, "SA統計")

        return {
            'results': all_results,
            'aggregate_csv': aggregate_csv,
            'stats_csv': stats_csv,
            'plot_filename': plot_filename,
            'total_time': total_time
        }
    else:
        print("処理できるデータがありませんでした。")
        return None

def main():
    """メイン実行関数"""
    print("=" * 60)
    print("多スタート焼きなまし法による生産スケジューリング")
    print("=" * 60)

    # データフォルダの確認
    if not os.path.exists(DATA_FOLDER):
        print(f"データフォルダ '{DATA_FOLDER}' が見つかりません。")
        return

    # バッチ処理実行
    result = run_multistart_batch()

    if result:
        print(f"\n=== 最終結果 ===")
        print(f"処理ファイル数: {len(result['results'])}")
        print(f"総処理時間: {result['total_time']:.2f}秒")
        print(f"集約CSV: {result['aggregate_csv']}")
        print(f"統計CSV: {result['stats_csv']}")
        print(f"プロット: {result['plot_filename']}")

        # 総コストの統計を表示
        total_costs = [r['総コスト'] for r in result['results']]
        calc_times = [r['計算時間'] for r in result['results']]

        print(f"\n=== 総コスト統計 ===")
        print(f"平均総コスト: {sum(total_costs)/len(total_costs):.2f}")
        print(f"最小総コスト: {min(total_costs):.2f}")
        print(f"最大総コスト: {max(total_costs):.2f}")

        print(f"\n=== 計算時間統計 ===")
        print(f"平均計算時間: {sum(calc_times)/len(calc_times):.2f}秒")
        print(f"最小計算時間: {min(calc_times):.2f}秒")
        print(f"最大計算時間: {max(calc_times):.2f}秒")
    else:
        print("処理に失敗しました。")

if __name__ == "__main__":
    main()
